export function markdownItSafeHtml(md) {
    const allowedTags = ['llm-mark', 'llm-cite']; // 你可以加更多自定义组件标签

    md.core.ruler.after('inline', 'escape-unknown-tags', state => {
        for (const blockToken of state.tokens) {
            if (blockToken.type !== 'inline') continue;

            for (const token of blockToken.children || []) {
                console.log(token);
                if (token.type === 'text') {
                    token.content = token.content.replace(/<[^>]+>/g, tag => {
                        const tagName = tag.match(/^<\/?([a-zA-Z0-9\-]+)/)?.[1];
                        if (!tagName || !allowedTags.includes(tagName)) {
                            return tag
                                .replace(/&/g, '&amp;')
                                .replace(/</g, '&lt;')
                                .replace(/>/g, '&gt;');
                        }
                        return tag;
                    });
                }
            }
        }
    });
}
