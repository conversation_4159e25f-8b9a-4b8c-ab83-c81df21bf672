import Icon from './icon/icon.vue';
import EditableText from './form/EditableText.vue';
import RpCollapse from './layout/Collapse.vue';
import RpClipboard from './clipboard/Clipboard.vue';
import RpGripLayout from './layout/GridLayout.vue';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import MDialog from './dialog/index';
import { ElMessage } from 'element-plus';

const customComponents = [
    Icon,
    EditableText,
    RpCollapse,
    RpClipboard,
    RpGripLayout
];
let dialogHooks = null;
export const useShowDialog = function (...args) {
    if (!dialogHooks) {
        throw new Error('[his-common] dialog is not installed');
    }
    return dialogHooks.showDialog.apply(this, args);
};
export const useShowDrawer = function (...args) {
    if (!dialogHooks) {
        throw new Error('[his-common] dialog is not installed');
    }
    return dialogHooks.showDrawer.apply(this, args);
};

export default function (app, router) {
    // 避免多次应用，dialog的实例被重置，只保留第一次的实例
    if (!dialogHooks) {
        dialogHooks = MDialog.create(app, router);
    }

    app.config.globalProperties.$toast = ElMessage;

    for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
        app.component(key, component);
    }
    customComponents.forEach(component => {
        app.component(component.name, component);
    });
}
