<script setup lang="ts">
import SettingsIcon from "@/components/icons/SettingsIcon.vue";
import VoiceInputIcon from "@/components/icons/VoiceInputIcon.vue";
import VoiceWaveIcon from "@/components/icons/VoiceWaveIcon.vue";
import { useVoiceStore } from '@/stores/voice';
import { storeToRefs } from 'pinia';
import { onMounted, onUnmounted } from 'vue';

// 使用voice store
const voiceStore = useVoiceStore();

// 使用storeToRefs解构响应式状态，保持响应性
const {
  // 状态 - 使用storeToRefs保持响应性
  isRecording,
  audioDevices,
  selectedDeviceId,
  hasPermission,
  isCheckingPermission,
  audioLevel,
} = storeToRefs(voiceStore);

// 方法直接从store解构（方法不需要响应性）
const {
  toggleRecording,
  requestMicrophonePermission,
  getAudioDevices,
  handleDeviceChange,
  openMicrophoneSettings,
  initializeVoice,
  cleanup
} = voiceStore;

// 组件挂载
onMounted(async () => {
  await initializeVoice();
});

// 组件卸载时清理
onUnmounted(() => {
  cleanup();
});
</script>

<template>
  <div class="voice-container">
    <!-- 录音按钮 -->
    <div class="record-button-container">
      <button class="record-button" :class="{ 'recording': isRecording }" @click="toggleRecording"
        :title="isRecording ? '停止录音' : '开始录音'">
        <VoiceInputIcon v-if="!isRecording" />
        <VoiceWaveIcon v-else :is-recording="true" :audio-level="audioLevel" />
      </button>
    </div>

    <div class="voice-tip">
      {{ isRecording ? '正在录音中...' : '开始录音前请确认已获得宠主同意' }}
    </div>

    <!-- 麦克风设置弹窗 -->
    <el-popover placement="top" :width="320" trigger="click" @before-enter="openMicrophoneSettings">
      <template #reference>
        <el-button class="settings-button" title="麦克风设置" :disabled="isCheckingPermission">
          <SettingsIcon />
        </el-button>
      </template>

      <div class="microphone-settings">
        <h4 class="settings-title">麦克风设置</h4>

        <!-- 权限状态显示 -->
        <div class="permission-status">
          <span class="status-label">权限状态：</span>
          <el-tag :type="hasPermission ? 'success' : 'danger'" size="small">
            {{ hasPermission ? '已授权' : '未授权' }}
          </el-tag>
          <el-button v-if="!hasPermission" type="primary" size="small" @click="requestMicrophonePermission"
            :loading="isCheckingPermission" style="margin-left: 8px;">
            获取权限
          </el-button>
        </div>

        <!-- 设备选择 -->
        <div class="device-selection" v-if="hasPermission">
          <span class="device-label">选择麦克风：</span>
          <el-select v-model="selectedDeviceId" placeholder="请选择麦克风设备" style="width: 100%; margin-top: 8px;"
            @change="handleDeviceChange">
            <el-option v-for="device in audioDevices" :key="device.deviceId" :label="device.label"
              :value="device.deviceId" />
          </el-select>

          <!-- 设备信息 -->
          <div class="device-info" v-if="audioDevices.length > 0">
            <el-text size="small" type="info">
              共找到 {{ audioDevices.length }} 个麦克风设备
            </el-text>
          </div>

          <!-- 无设备提示 -->
          <div class="no-device" v-else>
            <el-text size="small" type="warning">
              未找到可用的麦克风设备
            </el-text>
          </div>
        </div>

        <!-- 未授权提示 -->
        <div class="no-permission-tip" v-else>
          <el-text size="small" type="info">
            请先授权使用麦克风，然后可以选择设备
          </el-text>
        </div>

        <!-- 刷新按钮 -->
        <div class="refresh-section">
          <el-button size="small" @click="getAudioDevices" :disabled="!hasPermission">
            <el-icon style="margin-right: 6px;">
              <Refresh />
            </el-icon>
            刷新设备
          </el-button>
        </div>
      </div>
    </el-popover>
  </div>
</template>

<style scoped src="./VoiceInput.css"></style>