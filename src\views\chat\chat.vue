<template>
    <div class="chat">
        <el-container
            :class="{
                'is-sidebar-collapsed': isSidebarCollapsed
            }"
        >
            <chat-sidebar :is-sidebar-collapsed="isSidebarCollapsed" @update:is-sidebar-collapsed="handleSidebarChange" />
            <el-main class="chat-main">
                <mobile-chat-header :is-sidebar-collapsed="isSidebarCollapsed" @update:is-sidebar-collapsed="handleSidebarChange" />
                <chat-box
                    class="chat-box" v-if="chatBoxVisible"
                    ref="chatBoxRef"
                    :id="route.params.id"
                    :scene="route.query.scene"
                    :title="chatTitle"
                    :subtitle="chatSubTitle"
                    width="800px"
                />
            </el-main>
        </el-container>
        <!--
        <div class="assistant" @click="showKnowledgeAssistant">
            <rp-icon name="icon-assistant" size="40" />
            <span>知识助手</span>
        </div>
        -->
    </div>
</template>

<script setup>
import { watch, ref, nextTick, computed } from 'vue';
import ChatSidebar from './widgets/ChatSidebar.vue';
import ChatBox from './widgets/ChatBox.vue';
import MobileChatHeader from './widgets/MobileChatHeader.vue';
import { useRoute } from 'vue-router';
// import RpIcon from '@/ui/icon/icon.vue';
import { useShowDrawer } from '@/ui/index.js';
import { useEventBus, useLocalStorage } from '@vueuse/core';
const chatBoxRef = ref(null);
const route = useRoute();
const knowledgeAssistant = ref(null);
const chatBoxVisible = ref(true);
const bus = useEventBus(Symbol('chat'));
bus.on((event, payload) => {
    if (event === 'newChat') {
        knowledgeAssistant.value = payload;
    }
});
const isSidebarCollapsed = useLocalStorage('chat-sidebar', false);

// watch(() => route.query.t, () => {
//     chatBoxRef.value.resetChat();
// });
watch(route, () => {
    chatBoxVisible.value = false;
    nextTick(() => {
        chatBoxVisible.value = true;
    });
});

const chatTitle = computed(() => {
    if (route.query.scene === 'knowledge') {
        return '我是知识查询助手，需要我帮你查什么呢？';
    }
    return '我是好兽医AI助手，今天我能帮你做什么？';
});
const chatSubTitle = computed(() => {
    if (route.query.scene === 'knowledge') {
        return '关于疾病、药品相关的知识都可以问我哦~';
    }
    return '有什么问题可以随时问我~';
});
const handleSidebarChange = (collapsed) => {
    isSidebarCollapsed.value = collapsed;
};
const showKnowledgeAssistant = () => {
    useShowDrawer(ChatBox, {
        id: knowledgeAssistant.value ? knowledgeAssistant.value.id : '',
        scene: 'knowledge',
        title: '我是知识查询助手，需要我帮你查什么呢？',
        subtitle: '关于疾病、药品相关的知识都可以问我哦~',
        eventBus: bus,
        settings: {
            rows: 1,
            tools: {
                // 化验图片
                laboratory: false,
                // 影像图片
                image: false,
            }
        }
    }, {
        title: '知识查询',
        width: '500px',
        bodyClass: 'rp-dialog-body-chatbox',
    }).catch(e => {
    }).finally(() => {
        bus.reset();
    });
};
</script>

<style scoped lang="scss">
.is-mobile {
    .chat-box {
        padding: 0 20px;
        position: relative;
        top: 50px;
        height: calc(100% - 50px) !important;
        :deep(.chat-container-inner) {
            box-sizing: border-box;
        }
    }
}
.chat {
    height: 100%;
    .el-container {
        height: 100%;
    }
    .assistant {
        position: fixed;
        right: 20px;
        bottom: 20px;
        display: flex;
        flex-direction: column;
        align-items: center;
        align-content: center;
        cursor: pointer;
        &:hover {
            color: var(--el-color-primary);
        }
    }
}
</style>
