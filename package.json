{"name": "code", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview --host", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "lint:fix": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"@vueuse/core": "^13.1.0", "animate.css": "^4.1.1", "axios": "^1.8.4", "cos-js-sdk-v5": "^1.8.7", "dayjs": "^1.11.13", "element-plus": "^2.9.7", "highlight.js": "^11.11.1", "katex": "^0.16.22", "lodash": "^4.17.21", "markdown-it": "^14.1.0", "markdown-it-katex": "^2.0.3", "markdown-it-mermaid": "^0.2.5", "marked": "^15.0.8", "marked-highlight": "^2.2.1", "marked-katex-extension": "^5.1.4", "mermaid": "^11.6.0", "pinia": "^3.0.2", "pinia-plugin-persistedstate": "^4.2.0", "sass": "^1.86.3", "uuid": "^11.1.0", "vue": "^3.5.13", "vue-element-plus-x": "^1.3.0", "vue-i18n": "^11.1.3", "vue-router": "^4.5.0"}, "devDependencies": {"@vitejs/plugin-legacy": "^6.1.1", "@vitejs/plugin-vue": "^5.2.1", "eslint": "^9.24.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-vue": "^10.0.0", "terser": "^5.43.1", "vite": "^6.2.0"}, "description": "This template should help get you started developing with Vue 3 in Vite. The template uses Vue 3 `<script setup>` SFCs, check out the [script setup docs](https://v3.vuejs.org/api/sfc-script-setup.html#sfc-script-setup) to learn more.", "main": "vite.config.js", "keywords": [], "author": "", "license": "ISC"}