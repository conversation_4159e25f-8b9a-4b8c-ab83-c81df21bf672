<template>
    <div class="chat-message">
        <div class="message query">
            <!--<div class="message-content">{{ currentMessage.query }}</div>-->
            <div class="message-content">
                <div class="marked" v-html="currentMessage.query">
                </div>
                <!--<component :is="compileTemplate(currentMessage.query)" />-->
            </div>
        </div>
        <div class="message answer">
            <div class="avatar">
                <img src="https://img1.rpfieldcdn.com/ai/vet/logo.png" width="40" alt="">
            </div>
            <div class="message-content">
                <!--
                <div class="marked">
                    <component :is="compileTemplate(currentMessage.content)" />
                </div>
                -->
                <div class="marked" v-html="currentMessage.content"></div>
                <div class="actions">
                    <div class="action">
                        <el-tooltip effect="dark" content="复制" placement="top">
                            <rp-icon name="icon-copy" @click="copyAnswer" />
                        </el-tooltip>
                    </div>
                    <div class="action">
                        <el-tooltip effect="dark" content="喜欢" placement="top">
                            <rp-icon
                                name="icon-zan"
                                @click="putMessageFeedback('like')"
                                :color="currentMessage.feedback.rating==='like' ? 'var(--el-color-primary)' : '#333'"
                            />
                        </el-tooltip>
                    </div>
                    <div class="action">
                        <el-tooltip effect="dark" content="不喜欢" placement="top">
                            <rp-icon
                                name="icon-cai"
                                @click="putMessageFeedback('dislike')"
                                :color="currentMessage.feedback.rating==='dislike' ? 'var(--el-color-error)' : '#333'"
                            />
                        </el-tooltip>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { defineProps, ref, watchEffect, unref, compile, getCurrentInstance } from 'vue';
import { marked } from 'marked';
import { markedHighlight } from 'marked-highlight';
import markedKatex from 'marked-katex-extension';
import { http } from '@/extensions/http';
import { httpApis } from '@/hooks/useHttpApi';
import hljs from 'highlight.js';
import '@/styles/markdown.scss';
import 'katex/dist/katex.min.css';
import { ElMessageBox } from 'element-plus';
import { useToast } from '@/hooks/useToast';
const toast = useToast();

marked.use(markedHighlight({
    // The option `highlight` is passed to `require('highlight.js')`
    highlight(code, lang) {
        const language = hljs.getLanguage(lang) ? lang : 'plaintext';
        return hljs.highlight(code, {
            language
        }).value;
    }
}));
marked.use(markedKatex({
    throwOnError: false,
}));

const compileTemplate = html => {
    if (!html) {
        return null;
    }
    return compile(`<div>${html}</div>`, { });
};
const currentMessage = ref(null);

const props = defineProps({
    message: {
        type: Object,
        required: true
    }
});
const putMessageFeedback = rating => {
    let ratingValue = rating;
    if (currentMessage.value.feedback.rating === rating) {
        ratingValue = null;
    }
    if (ratingValue !== 'dislike') {
        saveMessageFeedback({
            messageId: currentMessage.value.id,
            rating: ratingValue,
            content: ''
        });
        return;
    }
    ElMessageBox.prompt('请告诉我不对的地方，以便更好的进行改进', '意见反馈', {
        confirmButtonText: '提交反馈',
        cancelButtonText: '取消',
    }).then(({
        value
    }) => {
        saveMessageFeedback({
            messageId: currentMessage.value.id,
            rating: ratingValue,
            content: value
        });
    });
};
/**
 * 拷贝内容到剪切板
 */
const copyAnswer = () => {
    // 移除think过程
    const content = currentMessage.value.answer
        .replace(/<think[^>]*>[\s\S]*<\/think>/g, '')
        .replace(/<llm-think[^>]*>[\s\S]*<\/llm-think>/g, '');
    if (navigator.clipboard) {
        navigator.clipboard.writeText(content).then(() => {
            toast.success('复制成功');
        }).catch(e => {
            console.log(e);
        });
    }
};
/**
 * 保存反馈信息
 * @param data
 */
const saveMessageFeedback = (data) => {
    http.post(httpApis.putMessageFeedback, data).then(() => {
        currentMessage.value.feedback.rating = data.rating;
    });
};
/**
 * 将html内容中缺失的标签补充完整
 * @param html
 */
const makeHtmlComplete = (html) => {
    if (!html) {
        return '';
    }
    // 使用DOMParser自动修复HTML标签闭合问题
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');
    return doc.body.innerHTML;
};

watchEffect(() => {
    const message = unref(props.message);
    message.query = makeHtmlComplete(marked.parse(message.query));
    let content = makeHtmlComplete(marked.parse(message.answer));
    content = content
        .replace(/^\s+/g, '')
        // .replace(/<\/?\w{0,5}$/g, '') // 避免不完整的mark,think标签展示出来
        .replace(/<\/?([a-zA-Z]{1,4})[^>]*$/g, (a, b) => {
            if (b.indexOf('mark') === 0) {
                return '';
            }
            return a;
        }) // 避免不完整的mark,think标签展示出来
        .replace(/^<details[^>]+>/g, '<llm-think class="md-think" status="' + message.status + '">')
        .replace(/<\/details>/g, '</llm-think>')
        .replace(/<summary>(.+?)<\/summary>/g, '')
        .replace(/^<think[^>]*>/g, '<llm-think class="md-think" status="' + message.status + '">')
        .replace(/<\/think>/g, '</llm-think>\n\n')
        .replace(/<(mark|think)/g, '<llm-$1')
        .replace(/<\/(mark|think)>/g, '</llm-$1>');
    message.content = content;
    currentMessage.value = message;
});
</script>

<style scoped lang="scss">
.chat-message {
    font-size: 14px;
    margin: 0 20rpx;
    .message {
        font-size: 16px;
        display: flex;
        padding: 10rpx 0;
        margin-bottom: 10px;
        .avatar {
            //width: 70rpx;
            //height: 70rpx;
            font-size: 14px;
            color: #555;
            margin-bottom: 4px;
            line-height: 25px;
            img {
                border: 1px solid #ddd;
                border-radius: 50%;
            }
        }
        .message-content {
            .footer {
                display: flex;
                align-items: center;
                margin-top: 5px;
            }
            .tips {
                flex: 1;
                font-size: 11px;
                color: #999;
            }
            .actions {
                .action-item {
                    margin: 0 10px;
                    &.active {
                        color: var(--el-color-primary);
                    }
                }
            }
        }
        &.query {
            justify-content: flex-end;
            .message-content {
                max-width: 80%;
                background-color: var(--el-color-primary);
                color: #fff;
                border-radius: 10px;
                padding: 8px;
            }
        }
        &.answer {
            //justify-content: flex-start;
            display: flex;
            background: #fff;
            padding: 8px;
            border-radius: 10px;
            .message-content {
                max-width: 100%;
                flex: 1;
                margin-left: 10px;
            }
        }
        .actions {
            display: flex;
            gap: 15px;
            .action {
                cursor: pointer;
            }
        }
    }
}
</style>
