<script setup lang="ts">
import { type TableData, updateTableCell, reconstructMarkdownTable } from "@/utils/markdownUtils";
import { computed, ref, nextTick } from "vue";
import { Edit } from "@element-plus/icons-vue";

// Props
interface Props {
  tableData: TableData;
}

// Emits
interface Emits {
  (e: "update", updatedMarkdown: string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const editingCell = ref<{ row: number; col: number } | null>(null);
const editingValue = ref("");
const inputRef = ref<HTMLInputElement>();

// 计算属性
const tableHeaders = computed(() =>
  props.tableData.headers.map(header => header.replace(/\*\*/g, '')) // 移除**符号
);
const tableRows = computed(() => props.tableData.rows);

// 检查是否为空表格（只有表头没有数据行，或数据行都为空）
const isEmptyTable = computed(() => {
  if (!tableRows.value || tableRows.value.length === 0) {
    return true;
  }

  // 检查是否所有数据行都为空
  return tableRows.value.every(row =>
    row.every(cell => !cell || cell.trim() === '')
  );
});

// 开始编辑单元格
const startEditing = async (rowIndex: number, colIndex: number) => {
  editingCell.value = { row: rowIndex, col: colIndex };
  editingValue.value = tableRows.value[rowIndex][colIndex] || "";
  
  await nextTick();
  if (inputRef.value) {
    inputRef.value.focus();
    inputRef.value.select();
  }
};

// 完成编辑
const finishEditing = () => {
  if (!editingCell.value) return;

  const { row, col } = editingCell.value;
  const newContent = editingValue.value.trim();

  // 检查内容是否有变化
  const originalContent = tableRows.value[row][col] || "";
  if (newContent === originalContent) {
    // 内容没有变化，直接取消编辑
    cancelEditing();
    return;
  }

  try {
    // 更新表格数据
    const updatedTableData = updateTableCell(props.tableData, row, col, newContent);

    // 重构为markdown并发送更新事件
    const updatedMarkdown = reconstructMarkdownTable(updatedTableData);
    emit("update", updatedMarkdown);

    // 清除编辑状态
    editingCell.value = null;
    editingValue.value = "";
  } catch (error) {
    console.error('更新表格数据时出错:', error);
    // 出错时取消编辑
    cancelEditing();
  }
};

// 取消编辑
const cancelEditing = () => {
  editingCell.value = null;
  editingValue.value = "";
};

// 处理键盘事件
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === "Enter") {
    event.preventDefault();
    finishEditing();
  } else if (event.key === "Escape") {
    event.preventDefault();
    cancelEditing();
  }
};

// 检查是否正在编辑某个单元格
const isEditing = (rowIndex: number, colIndex: number): boolean => {
  return editingCell.value?.row === rowIndex && editingCell.value?.col === colIndex;
};
</script>

<template>
  <!-- 只有在表格不为空时才显示 -->
  <div v-if="!isEmptyTable" class="editable-table-container">
    <table class="editable-table">
      <thead>
        <tr>
          <th v-for="(header, index) in tableHeaders" :key="`header-${index}`" class="table-header">
            {{ header }}
          </th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(row, rowIndex) in tableRows" :key="`row-${rowIndex}`" class="table-row">
          <td
            v-for="(cell, colIndex) in row"
            :key="`cell-${rowIndex}-${colIndex}`"
            class="table-cell"
            :class="{
              'editing': isEditing(rowIndex, colIndex),
              'empty': !cell || cell.trim() === ''
            }"
            @click="startEditing(rowIndex, colIndex)"
            :title="isEditing(rowIndex, colIndex) ? '按Enter保存，按Esc取消' : '点击编辑'"
          >
            <input
              v-if="isEditing(rowIndex, colIndex)"
              ref="inputRef"
              v-model="editingValue"
              class="cell-input"
              @blur="finishEditing"
              @keydown="handleKeydown"
            />
            <span v-else class="cell-content">{{ cell || '' }}</span>
            <!-- 编辑图标 -->
            <el-icon
              v-if="!isEditing(rowIndex, colIndex)"
              class="edit-icon"
            >
              <Edit />
            </el-icon>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<style scoped src="./EditableTable.css"></style>
