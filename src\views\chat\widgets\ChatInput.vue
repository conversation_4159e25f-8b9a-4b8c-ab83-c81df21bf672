<template>
    <div class="input-area">
        <slot></slot>
        <div class="input-box" @click="inputRef.focus()">
            <div class="laboratory-values" v-if="laboratoryValues.length > 0">
                <div class="label">化验结果：</div>
                <div class="laboratory-result" v-for="(item, idx) in laboratoryValues" :key="idx">
                    <el-popover placement="top" :width="600" trigger="click">
                        <template #reference>
                            <img :src="item.image" alt="">
                        </template>
                        <div class="marked" style="max-width: 100%;max-height: 400px;overflow-y: auto">
                            <markdown-renderer :content="item.text" />
                        </div>
                        <div>{{ item.remark }}</div>
                    </el-popover>
                </div>
            </div>
            <div class="image-report-values" v-if="imageReportValues.length > 0">
                <div class="label">影像报告：</div>
                <div class="image-report" v-for="(item, idx) in imageReportValues" :key="idx">
                    <el-popover placement="top" :width="600" trigger="click">
                        <template #reference>
                            <img :src="item.image" alt="">
                        </template>
                        <div class="marked" style="max-width: 100%;max-height: 400px;overflow-y: auto">
                            <markdown-renderer :content="item.text" />
                        </div>
                    </el-popover>
                </div>
            </div>
            <voice-input
                v-model="inputMessage"
                ref="inputRef"
                type="textarea"
                :rows="settings.rows"
                autofocus
                autosize
                placeholder="有什么问题可以随时问我~"
                v-enter="sendMessage"
            />
            <div class="footer">
                <div class="tools">
                    <div class="tool" v-if="settings.tools.laboratory">
                        <el-button round size="small" icon="upload" @click.stop.prevent="uploadLaboratoryReport">化验结果</el-button>
                    </div>
                    <div class="tool" v-if="settings.tools.image">
                        <el-button round size="small" icon="upload" @click.stop.prevent="uploadImageReport">影像报告</el-button>
                    </div>
                </div>
                <div class="btn-group">
                    <div class="tool">
                        <rp-icon v-if="isRunning" name="icon-circle-stop" size="22" @click="stopMessage" />
                        <rp-icon v-else name="icon-send" @click="sendMessage" size="22" :color="laboratoryValues.length > 0 || inputMessage.trim() ? 'var(--el-color-primary)' : 'var(--el-color-info)'" />
                    </div>
                </div>
            </div>
        </div>
        <slot name="bottom"></slot>
    </div>
</template>

<script setup>
import { ref, defineProps, onMounted } from 'vue';
import { useUserStore } from '@/stores/user';
import RpIcon from '@/ui/icon/icon.vue';
import LaboratoryOcrView from './laboratory/ocr.vue';
import ImageOcrView from './image/ocr.vue';
import { useShowDialog } from '@/ui/index';
import MarkdownRenderer from '@/components/markdown/MarkdownRenderer.vue';
import VoiceInput from '@/ui/form/VoiceInput.vue';
const userStore = useUserStore();
const emit = defineEmits(['sendMessage', 'stopMessage']);
const inputMessage = ref('');
const inputRef = ref(null);
// 上传的化验结果数据
const laboratoryValues = ref([]);
const imageReportValues = ref([]);
const props = defineProps({
    isRunning: {
        type: Boolean,
        default: false
    },
    settings: {
        type: Object,
        default: () => {
            return {
                rows: 3,
                tools: {}
            };
        }
    }
});

onMounted(() => {
    setTimeout(() => {
        inputRef.value.focus();
    }, 300);
});

const renderLaboratoryResult = () => {
    return laboratoryValues.value.map(report => {
        const pet = report.pet;
        const petText = pet ? `宠物信息（年龄：${pet.age}，体重：${pet.weight}，种类：${pet.species}，品种：${pet.breed}，性别：${pet.gender}）` : '';
        const remark = report.remark ? `备注：${report.remark}` : '';
        return `
${petText}
${report.text}
${remark}

`;
    }).join('\n-----\n');
};

const renderImageReportResult = () => {
    return imageReportValues.value.map(report => {
        const pet = report.pet;
        const petText = pet ? `宠物信息（年龄：${pet.age}，体重：${pet.weight}，种类：${pet.species}，品种：${pet.breed}，性别：${pet.gender}）` : '';
        return `${petText}\n\n${report.text}`;
    }).join('\n-----\n');
};

const sendMessage = async () => {
    if (props.isRunning) return;
    const user = await userStore.checkLoginState();
    if (!user) {
        return;
    }
    const messages = [];
    if (laboratoryValues.value.length > 0) {
        messages.push(renderLaboratoryResult());
    }
    if (imageReportValues.value.length > 0) {
        messages.push(renderImageReportResult());
    }
    messages.push(inputMessage.value.trim());
    // const message = `${renderLaboratoryResult()}\n${inputMessage.value.trim()}`;
    const message = messages.join('\n\n');
    if (!message.trim()) return;
    emit('sendMessage', message);
    setTimeout(() => {
        inputMessage.value = '';
    }, 100);
    laboratoryValues.value = [];
    imageReportValues.value = [];
};
const stopMessage = () => {
    emit('stopMessage');
};
const uploadLaboratoryReport = () => {
    useShowDialog(LaboratoryOcrView, {}, {
        title: '上传化验结果',
        width: '880px',
        closeOnPressEscape: false
    }).then(data => {
        console.log(data);
        // emit('sendMessage', data);
        laboratoryValues.value.push(...data);
    });
};
const uploadImageReport = () => {
    useShowDialog(ImageOcrView, {}, {
        title: '上传影像报告',
        width: '880px',
        closeOnPressEscape: false
    }).then(data => {
        console.log(data);
        // emit('sendMessage', data);
        imageReportValues.value.push(...data);
    });
};
</script>

<style lang="scss" scoped>
.input-area {
    background: #fff;
    border-radius: 10px 10px 0 0;
    .input-box {
        border: 1px solid #ddd;
        box-shadow: 0 10px 15px -3px #0000001a, 0 4px 6px -4px #0000001a;
        background: #fff;
        border-radius: 10px;
        :deep(textarea) {
            border: none;
            box-shadow: none;
            border-radius: 10px;
            background: transparent;
        }
        :deep(.voice-input .btn) {
            right: 40px;
            bottom: -31px;
        }
        .footer {
            display: flex;
            align-content: center;
            padding: 5px 10px;
            font-size: 14px;
            .tools {
                flex: 1;
                display: flex;
                gap: 10px;
            }
            .tool {
                display: flex;
                align-content: center;
                align-items: center;
                cursor: pointer;
                &:hover {
                    color: var(--el-color-primary);
                }
            }
        }
    }
    .laboratory-values, .image-report-values {
        padding: 5px 10px;
        display: flex;
        align-items: baseline;
        gap: 5px;
        .label {
            color: #666;
            font-size: 12px;
        }
        .laboratory-result, .image-report {
            background: #f5f5f5;
            border: 1px solid #eee;
            cursor: pointer;
            border-radius: 5px;
            overflow: hidden;
            line-height: 1;
            img {
                width: 40px;
                height: 40px;
            }
        }
    }
}
</style>
