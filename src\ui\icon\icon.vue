<script lang="jsx">
import { h, resolveComponent } from 'vue';
import { ElIcon } from 'element-plus';
export default {
    name: 'RpIcon',
    props: {
        size: {
            type: [String, Number],
            default: 'inherit'
        },
        color: {
            type: String,
            default: 'inherit'
        },
        name: {
            type: String,
            default: ''
        }
    },
    render() {
        if (this.name.startsWith('icon-')) {
            return h('i', {
                class: `iconfont ${this.name}`,
                style: {
                    fontSize: this.size + 'px',
                    color: this.color,
                    height: '1em',
                    width: '1em',
                    lineHeight: '1em'
                }
            });
        }
        const IconVue = resolveComponent(`${this.name}`);
        return h(ElIcon, {
            size: this.size,
            color: this.color
        }, {
            default: () => h(IconVue)
        });
    }
};
</script>

<style scoped>
.iconfont, .el-icon {
    vertical-align: middle;
}
</style>
