<template>
    <div class="md-think">
        <div class="header">
            <span @click="toggleContent">
                <rp-icon v-if="props.status === 0" name="loading" class="is-loading" style="vertical-align: -1px;" />
                {{ statusText }}
                <rp-icon :name="showContent ? 'ArrowUp' : 'ArrowDown'" style="vertical-align: -1px;" />
            </span>
        </div>
        <div class="content" v-show="showContent">
            <slot></slot>
        </div>
    </div>
</template>

<script setup>
import { computed, defineProps, ref } from 'vue';
import RpIcon from '@/ui/icon/icon.vue';
const showContent = ref(true);
const props = defineProps({
    status: {
        type: Number,
        default: 0
    }
});
const statusText = computed(() => {
    if (props.status === 1) {
        return '已完成思考';
    }
    else if (props.status === -1) {
        return '已停止思考';
    }
    return '正在思考中…';
});
const toggleContent = () => {
    showContent.value = !showContent.value;
};
const hideContent = () => {
    showContent.value = false;
};
defineExpose({
    hideContent
});
</script>

<style scoped lang="scss">
.md-think {
    display: block;
    font-size: 13px;
    color: #666;
    margin-bottom: 10px;
    .header {
        font-weight: 500;
        span {
            cursor: pointer;
            background: #ededed;
            padding: 5px 10px;
            margin-top: 10px;
            border-radius: 5px;
        }
    }
    .content {
        margin-top: 0.8em;
        border-left: 2px solid #ddd;
        padding-left: 5px;
    }
    :deep(mark) {
        background: none;
        color: inherit;
    }
}
</style>
