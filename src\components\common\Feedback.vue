<script setup lang="ts">
import { LoginService, type FeedbackInfo } from "@/services/loginService";
import { computed, reactive, ref } from "vue";

// Props
interface Props {
  modelValue: boolean;
}

// Emits
interface Emits {
  (e: "update:modelValue", value: boolean): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 反馈类型选项
const feedbackTypes = [
  { label: "功能建议", value: 1 },
  { label: "界面设计", value: 2 },
  { label: "性能问题", value: 3 },
  { label: "其他问题", value: 4 },
];

// 表单数据
const formData = reactive<FeedbackInfo>({
  feedback_type: 1,
  feedback_content: "",
  contact_email: "",
  contact_phone: "",
});

// 表单引用和状态
const formRef = ref();
const loading = ref(false);

// 表单验证规则
const rules = {
  feedback_content: [
    { required: true, message: "请输入反馈内容", trigger: "blur" },
    {
      min: 1,
      max: 500,
      message: "反馈内容长度在 1 到 500 个字符",
      trigger: "blur",
    },
  ],
  contact_email: [
    {
      pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      message: "请输入正确的邮箱地址",
      trigger: "blur",
    },
  ],
  contact_phone: [
    {
      pattern: /^1[3-9]\d{9}$/,
      message: "请输入正确的手机号码",
      trigger: "blur",
    },
  ],
};

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit("update:modelValue", value),
});

// 选择反馈类型
const selectFeedbackType = (type: number) => {
  formData.feedback_type = type;
};

// 重置表单
const resetForm = () => {
  formData.feedback_type = 1;
  formData.feedback_content = "";
  formData.contact_email = "";
  formData.contact_phone = "";
  formRef.value?.clearValidate();
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  resetForm();
};

// 提交反馈
const submitFeedback = async () => {
  try {
    // 表单验证
    await formRef.value?.validate();

    loading.value = true;

    // 调用API提交反馈
    await LoginService.createFeedbackFromApi(formData);
    handleClose();
  } finally {
    loading.value = false;
  }
};
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    title="意见反馈"
    width="35dvw"
    :before-close="handleClose"
    class="feedback-dialog"
    center
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-position="top"
      class="feedback-form"
    >
      <!-- 反馈类型 -->
      <div class="form-section">
        <h3 class="section-title">反馈类型</h3>
        <div class="feedback-types">
          <div
            v-for="type in feedbackTypes"
            :key="type.value"
            :class="[
              'feedback-type-item',
              { active: formData.feedback_type === type.value },
            ]"
            @click="selectFeedbackType(type.value)"
          >
            {{ type.label }}
          </div>
        </div>
      </div>

      <!-- 反馈内容 -->
      <div class="form-section">
        <h3 class="section-title">反馈内容</h3>
        <el-form-item prop="feedback_content">
          <el-input
            v-model="formData.feedback_content"
            type="textarea"
            placeholder="请详细描述您的建议..."
            :rows="6"
            maxlength="500"
            show-word-limit
            class="feedback-textarea"
          />
        </el-form-item>
      </div>

      <!-- 联系方式 -->
      <div class="form-section">
        <h3 class="section-title">联系方式</h3>
        <el-form-item prop="contact_email">
          <el-input
            v-model="formData.contact_email"
            placeholder="请输入邮箱地址"
            class="contact-input"
          >
            <template #prefix>
              <el-icon><Message /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item prop="contact_phone">
          <el-input
            v-model="formData.contact_phone"
            placeholder="请输入手机号码"
            class="contact-input"
          >
            <template #prefix>
              <el-icon><Phone /></el-icon>
            </template>
          </el-input>
        </el-form-item>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" class="cancel-button"> 取消 </el-button>
        <el-button
          type="primary"
          @click="submitFeedback"
          :loading="loading"
          class="submit-button"
        >
          提交反馈
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped src="./Feedback.css"></style>
