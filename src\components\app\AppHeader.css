.app-header {
  height: var(--app-header-height);
  display: grid;
  grid-template-columns: 1fr 4fr 1fr;
  align-items: center;
  background: white linear-gradient(145deg, #87ceeb 0%, #6097fc 50%, rgb(255, 107, 230,0.7) 100%);
}

.app-header > img {
  height: var(--app-header-height);
  cursor: pointer;
}

.nav-links {
  display: flex;
  gap: 2dvw;
  a {
    color: rgba(255, 255, 255, 0.86);
    text-decoration: none;
    font-size: 0.9dvw;
    font-weight: bold;
    letter-spacing: 0.1dvw;
    padding: 0.4dvw 0.8dvw;
    border-radius: 0.4dvw;
    border: 0.15dvw solid transparent;
    transition:
      color 0.18s ease,
      text-shadow 0.18s ease;
  }
  a:hover {
    background-color: transparent;
    box-shadow: none;
    border-color: transparent;
    color: #fff;
    text-shadow:
      0 0 0.2dvw rgba(255, 255, 255, 0.65),
      0 0 0.4dvw rgba(236, 179, 255, 0.4),
      0 0 0.6dvw rgba(236, 179, 255, 0.25);
    cursor: pointer;
  }
}

.app-header-user {
  text-align: right;
  padding-right: 1.2dvw;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* 用户下拉菜单样式 */
.user-dropdown {
  cursor: pointer;
}

.user-avatar {
  width: 4.7dvh;
  height: 4.7dvh;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.user-avatar:hover {
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

/* 下拉菜单样式 */
.user-dropdown-menu {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  min-width: 6dvw;
}