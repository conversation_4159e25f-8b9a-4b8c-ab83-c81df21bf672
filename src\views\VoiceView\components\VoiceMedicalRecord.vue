<script setup lang="ts">
import { type UploadedFileInfo, type VoiceMedicalRecord } from '@/services/voiceService';
import { useMedicalRecordStore } from '@/stores/medicalRecord';
import { TokenCookieManager } from "@/utils/cookieUtils";
import { ElMessage, type UploadProps, type UploadUserFile } from 'element-plus';
import { debounce } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { onMounted, onUnmounted, ref, watch } from 'vue';

// 全局的Pina库
const medicalRecordStore = useMedicalRecordStore();

// 全局的响应数据
const {
    medicalRecord
} = storeToRefs(medicalRecordStore);

// 全局的方法
const {
    updateVoiceMedicalRecord
} = medicalRecordStore;

// 本组件的响应数据
const loading = ref(false)
const dialogImageUrl = ref('')
const dialogVisible = ref(false)
const inspectionReportFiles = ref<UploadUserFile[]>([])
const imageReportFiles = ref<UploadUserFile[]>([])

// 本组件的方法
const handleFormChange = debounce(async (newValue: VoiceMedicalRecord, _oldValue: VoiceMedicalRecord): Promise<void> => {
    try {
        // 检查是否有有效数据
        if (!newValue) {
            console.warn('表单数据为空，跳过更新');
            return;
        }
        // 设置加载状态
        loading.value = true;
        await updateVoiceMedicalRecord(newValue);
    } catch (error) {
        console.error('更新医疗记录失败:', error);
        ElMessage.error('保存失败，请稍后重试');
    } finally {
        // 清除加载状态
        loading.value = false;
    }
}, 1000);
const handlePictureCardPreview: UploadProps['onPreview'] = (uploadFile) => {
    dialogImageUrl.value = uploadFile.url!
    dialogVisible.value = true
}
const handleInspectionReportRemove: UploadProps['onRemove'] = (_uploadFile, uploadFiles) => {
    const inspectionReportIds = []
    if (uploadFiles && uploadFiles.length > 0) {
        inspectionReportIds.push(...uploadFiles.filter((file: any) => file.status === "success")
            .map((file: any) => file.response?.data?.file_id ?? file.uid))
    }
    medicalRecord.value = { ...medicalRecord.value, inspection_report_ids: inspectionReportIds }
}
const handleImageReportRemove: UploadProps['onRemove'] = (_uploadFile, uploadFiles) => {
    const imageReportIds = []
    if (uploadFiles && uploadFiles.length > 0) {
        imageReportIds.push(...uploadFiles.filter((file: any) => file.status === "success")
            .map((file: any) => file.response?.data?.file_id ?? file.uid))
    }
    medicalRecord.value = { ...medicalRecord.value, image_report_ids: imageReportIds }
}
const handleInspectionReportSuccess: UploadProps['onSuccess'] = (_response, _uploadFile, uploadFiles) => {
    const inspectionReportIds = []
    if (uploadFiles && uploadFiles.length > 0) {
        inspectionReportIds.push(...uploadFiles.filter((file: any) => file.status === "success")
            .map((file: any) => file.response?.data?.file_id ?? file.uid))
    }
    medicalRecord.value = { ...medicalRecord.value, inspection_report_ids: inspectionReportIds }
}

const handleImageReportSuccess: UploadProps['onSuccess'] = (_response, _uploadFile, uploadFiles) => {
    const imageReportIds = []
    if (uploadFiles && uploadFiles.length > 0) {
        imageReportIds.push(...uploadFiles.filter((file: any) => file.status === "success")
            .map((file: any) => file.response?.data?.file_id ?? file.uid))
    }
    medicalRecord.value = { ...medicalRecord.value, image_report_ids: imageReportIds }
}

// 本组件的监听器
watch(
    medicalRecord,
    (newValue, oldValue) => {
        if (newValue && oldValue) {
            handleFormChange(newValue, oldValue);
        }
    },
    {
        deep: true,
        immediate: false
    }
);

onMounted(() => {
    const inspectionReportList = medicalRecord.value?.inspection_report_files
    const imageReportList = medicalRecord.value?.image_report_files
    if (inspectionReportList && inspectionReportList.length > 0) {
        inspectionReportFiles.value = inspectionReportList.map((file: UploadedFileInfo) => {
            return {
                uid: file.file_id,
                url: file.url,
                name: file.cos_path.slice(file.cos_path.lastIndexOf('/') + 1, file.cos_path.lastIndexOf('.')),
                status: 'success'
            }
        })
    }
    if (imageReportList && imageReportList.length > 0) {
        imageReportFiles.value = imageReportList.map((file: UploadedFileInfo) => {
            return {
                uid: file.file_id,
                url: file.url,
                name: file.cos_path.slice(file.cos_path.lastIndexOf('/') + 1, file.cos_path.lastIndexOf('.')),
                status: 'success'
            }
        })
    }


});

onUnmounted(() => {
    handleFormChange.cancel();
});

</script>

<template>
    <div class="collapse-layout">
        <el-form v-loading="loading" :model="medicalRecord" label-width="auto" label-position="top"
            class="collapse-form">
            <el-collapse class="collapse-container">
                <el-collapse-item title="基本信息">
                    <div class="collapse-base-info">
                        <el-form-item label="宠物名" prop="pet_name">
                            <el-input v-model="medicalRecord!.pet_name" placeholder="请输入宠物的名字" />
                        </el-form-item>
                        <el-form-item label="性别" prop="pet_gender">
                            <el-radio-group v-model="medicalRecord!.pet_gender">
                                <el-radio value="MALE">公</el-radio>
                                <el-radio value="FEMALE">母</el-radio>
                                <el-radio value="UNKNOWN">未知</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="年龄" placeholder="请输入宠物名的名称" prop="pet_name">
                            <el-input-number :min="0" class="pet-age-input-number"
                                v-model="medicalRecord!.pet_age_years" :controls="false" />
                            <span class="pet-age-title">年</span>
                            <el-input-number :min="0" class="pet-age-input-number"
                                v-model="medicalRecord!.pet_age_months" :controls="false" />
                            <span class="pet-age-title">月</span>
                        </el-form-item>
                        <el-form-item label="绝育状态" prop="pet_is_neutered">
                            <el-radio-group v-model="medicalRecord!.pet_is_neutered">
                                <el-radio value="YES">已绝育</el-radio>
                                <el-radio value="NO">未绝育</el-radio>
                                <el-radio value="UNKONW">未知</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="种类" prop="pet_species">
                            <el-radio-group v-model="medicalRecord!.pet_species">
                                <el-radio value="DOG">犬</el-radio>
                                <el-radio value="CAT">猫</el-radio>
                                <el-radio value="OTHER">其他</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="品种" prop="pet_breed">
                            <el-input v-model="medicalRecord!.pet_breed" placeholder="请输入宠物的品种" />
                        </el-form-item>
                    </div>
                </el-collapse-item>
                <el-collapse-item title="主观信息">
                    <el-form-item label="主诉" prop="chief_complaint">
                        <el-input type="textarea" v-model="medicalRecord!.chief_complaint"
                            :autosize="{ minRows: 2, maxRows: 5 }" placeholder="请输入宠物的主诉" />
                    </el-form-item>
                    <el-form-item label="既往病史" prop="past_history">
                        <el-input type="textarea" v-model="medicalRecord!.past_history"
                            :autosize="{ minRows: 2, maxRows: 5 }" placeholder="请输入宠物的既往病史" />
                    </el-form-item>
                    <el-form-item label="现病史" prop="present_illness">
                        <el-input type="textarea" v-model="medicalRecord!.present_illness"
                            :autosize="{ minRows: 2, maxRows: 5 }" placeholder="请输入宠物的现病史" />
                    </el-form-item>
                </el-collapse-item>
                <el-collapse-item title="体格检查">
                    <div class="collapse-body-info">
                        <el-form-item label="体重 (kg)" prop="pet_weight">
                            <el-input-number :min="0" v-model="medicalRecord!.pet_weight" controls-position="right"
                                :precision="2" class="pet-body-input-number" />
                        </el-form-item>
                        <el-form-item label="体温 (°C)" prop="pet_temperature">
                            <el-input-number :min="0" v-model="medicalRecord!.pet_temperature" controls-position="right"
                                :precision="2" :step="0.1" class="pet-body-input-number" />
                        </el-form-item>
                        <el-form-item label="心率 (次/分)" prop="pet_heart_rate">
                            <el-input-number :min="0" v-model="medicalRecord!.pet_heart_rate" controls-position="right"
                                class="pet-body-input-number" />
                        </el-form-item>
                        <el-form-item label="呼吸频率 (次/分)" prop="pet_respiratory_rate">
                            <el-input-number :min="0" v-model="medicalRecord!.pet_respiratory_rate"
                                controls-position="right" class="pet-body-input-number" />
                        </el-form-item>
                    </div>
                    <el-form-item label="体况描述" prop="body_desc">
                        <el-input type="textarea" v-model="medicalRecord!.body_desc"
                            :autosize="{ minRows: 2, maxRows: 5 }" placeholder="请输入宠物的体况描述" />
                    </el-form-item>
                </el-collapse-item>
                <el-collapse-item title="客观检查">
                    <el-form-item label="检查检验报告" prop="inspection_report_ids">
                        <el-upload :multiple="true" v-model:file-list="inspectionReportFiles"
                            action="/api/voice-medical-records/upload_image"
                            :headers="{ Authorization: 'Bearer ' + TokenCookieManager.getToken() }"
                            list-type="picture-card" :on-preview="handlePictureCardPreview"
                            :on-remove="handleInspectionReportRemove" :on-success="handleInspectionReportSuccess">
                            <el-icon>
                                <Plus />
                            </el-icon>
                        </el-upload>
                    </el-form-item>
                    <el-form-item label="影像报告" prop="image_report_ids">
                        <el-upload :multiple="true" v-model:file-list="imageReportFiles"
                            action="/api/voice-medical-records/upload_image"
                            :headers="{ Authorization: 'Bearer ' + TokenCookieManager.getToken() }"
                            list-type="picture-card" :on-preview="handlePictureCardPreview"
                            :on-remove="handleImageReportRemove" :on-success="handleImageReportSuccess">
                            <el-icon>
                                <Plus />
                            </el-icon>
                        </el-upload>
                    </el-form-item>
                </el-collapse-item>
                <el-collapse-item title="诊断与治疗方案">
                    <el-form-item label="诊断" prop="diagnosis">
                        <el-input type="textarea" v-model="medicalRecord!.diagnosis"
                            :autosize="{ minRows: 2, maxRows: 5 }" placeholder="请输入诊断内容" />
                    </el-form-item>
                    <el-form-item label="治疗方案" prop="treatment">
                        <el-input type="textarea" v-model="medicalRecord!.treatment"
                            :autosize="{ minRows: 2, maxRows: 5 }" placeholder="请输入治疗方案" />
                    </el-form-item>
                    <el-form-item label="医嘱" prop="advice">
                        <el-input type="textarea" v-model="medicalRecord!.advice" :autosize="{ minRows: 2, maxRows: 5 }"
                            placeholder="请输入医嘱" />
                    </el-form-item>
                </el-collapse-item>
            </el-collapse>
        </el-form>
        <el-dialog v-model="dialogVisible">
            <img w-full :src="dialogImageUrl" alt="Preview Image" />
        </el-dialog>
    </div>
</template>

<style scoped src="./VoiceMedicalRecord.css"></style>