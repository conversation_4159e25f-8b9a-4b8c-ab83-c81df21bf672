import { uploadCosFile } from '@/hooks/useCos.js';
import { http } from '@/extensions/http.js';
import { httpApis } from '@/hooks/useHttpApi.js';

export const TASK_STATUS_INIT = 1;
export const TASK_STATUS_RUNNING = 2;
export const TASK_STATUS_FINISH = 3;
export const TASK_STATUS_ERROR = 4;

class VoiceMedicalRecordTask {
    constructor(thunks, text) {
        this.status = TASK_STATUS_INIT;
        this._thunks = thunks;
        this._text = text;
        this._url = '';
        this._result = null;
        // this.queue = ['uploadTest', 'createTest'];
        this.queue = ['upload', 'create'];
    }

    async _uploadTest() {
        throw new Error('upload error');
    }
    async _createTest() {
        throw new Error('create error');
    }

    async _upload() {
        const chunks = this._thunks;
        console.log('chunks', chunks.length);
        const length = chunks.reduce((acc, cur) => acc + cur.length, 0);
        const pcmBuffer = new Int16Array(length);

        let offset = 0;
        for (const chunk of chunks) {
            pcmBuffer.set(chunk, offset);
            offset += chunk.length;
        }

        const blob = new Blob([pcmBuffer.buffer], {
            type: 'application/octet-stream'
        });
        blob.name = 'voice.pcm';
        console.log('file', blob);
        const cos = await uploadCosFile(blob, {}, 'vma');
        this._url = cos.url;
        return cos;
    }
    async _create() {
        const res = await http.post(httpApis.createVoiceMedicalRecord, {
            url: this._url,
            text: this._text
        }, {
            local: true,
            timeout: 0
        });
        this._result = res.data;
        return res.data;
    }

    async _handleQueue() {
        this.status = TASK_STATUS_RUNNING;
        try {
            while (this.queue.length) {
                const fn = this.queue[0];
                await this[`_${fn}`]();
                this.queue.shift();
            }
        }
        catch (error) {
            console.error(error);
            this.status = TASK_STATUS_ERROR;
            return;
        }
        this.status = TASK_STATUS_FINISH;
    }
    async start() {
        if (this.status !== TASK_STATUS_INIT) {
            return;
        }
        await this._handleQueue();
        return [this._result, this.status];
    }

    async retry() {
        if (this.status !== TASK_STATUS_ERROR) {
            return;
        }
        await this._handleQueue();
        return [this._result, this.status];
    }
}

export const useVoiceMedicalRecordTask = (thunks, text) => {
    const task = new VoiceMedicalRecordTask(thunks, text);
    return task;
};
