:deep(.el-dialog > header) {
  display: none;
}

.login-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(145deg, #87ceeb 0%, #6097fc 50%, #ff69e6 100%);
  height: 65dvh;
  position: relative;
  overflow: hidden;
  border-radius: 1dvw;
}

/* Add futuristic animated background overlay */
.login-content::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(
      circle at 20% 80%,
      rgba(0, 255, 255, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 20%,
      rgba(0, 87, 255, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 40% 40%,
      rgba(255, 255, 255, 0.05) 0%,
      transparent 50%
    );
  animation: backgroundPulse 4s ease-in-out infinite alternate;
  pointer-events: none;
}

@keyframes backgroundPulse {
  0% {
    opacity: 0.3;
  }
  100% {
    opacity: 0.7;
  }
}

.login-content-left {
  flex: 1;
  position: relative;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.left-content-container {
  width: 100%;
  max-width: 90%;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 2dvw;
}

.logo-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2dvw;
  margin-bottom: 1dvw;
  position: relative;
}

.centered-logo {
  width: 9dvw;
  height: auto;
  filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.4));
  z-index: 2;
}

.feature-icons-left,
.feature-icons-right {
  display: flex;
  flex-direction: column;
  gap: 2dvw;
  align-items: center;
}

.floating-icon {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5dvw;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 400;
  text-align: center;
  animation: floatUpDown 3s ease-in-out infinite;
  --icon-size: 2dvw;
}

.floating-icon:nth-child(1) {
  animation-delay: 0s;
}

.floating-icon:nth-child(2) {
  animation-delay: 1.5s;
}

.feature-icons-right .floating-icon:nth-child(1) {
  animation-delay: 0.75s;
}

.feature-icons-right .floating-icon:nth-child(2) {
  animation-delay: 2.25s;
}

.floating-icon .icon-wrapper {
  color: rgba(255, 255, 255, 0.6);
  filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.2));
}

.floating-icon span {
  font-size: 0.9dvw;
  color: rgba(255, 255, 255, 0.7);
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.2);
  white-space: nowrap;
}

@keyframes floatUpDown {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

.intro-section {
  margin-bottom: 1.5dvw;
}

.app-title {
  font-size: 2.2dvw;
  font-weight: 700;
  color: white;
  margin: 0 0 1dvw 0;
  text-shadow: 0 0 20px rgba(255, 255, 255, 0.6);
  background: linear-gradient(135deg, #ffffff 0%, #e0f7ff 50%, #87ceeb 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: 0.1dvw;
}

.app-description {
  font-size: 1dvw;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  margin: 0;
  font-weight: 400;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
  max-width: 85%;
  margin: 0 auto;
}

.features-section {
  display: flex;
  flex-direction: column;
  gap: 1.5dvw;
  width: 100%;
}

.feature-category {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(15px);
  border-radius: 15px;
  padding: 1.5dvw;
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.category-title {
  font-size: 1.2dvw;
  font-weight: 600;
  color: white;
  margin: 0 0 1dvw 0;
  text-align: center;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.4);
  letter-spacing: 0.05dvw;
}

.feature-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.8dvw;
  justify-content: center;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.5dvw;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 0.6dvw 1.2dvw;
  border-radius: 25px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.feature-item:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(0, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  font-size: 1.2dvw;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-text {
  font-size: 0.9dvw;
  color: white;
  font-weight: 500;
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
}

.login-content-right {
  display: flex;
  flex-direction: column;
  flex: 1;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  height: 100%;
  align-items: center;
  justify-content: center;
  gap: 1.2dvw;
  color: white;
  position: relative;
  border-left: 1px solid rgba(255, 255, 255, 0.2);
  padding-inline: 2dvw;
  box-shadow:
    inset 0 0 50px rgba(255, 255, 255, 0.1),
    0 0 50px rgba(0, 0, 0, 0.1);
}

.login-content-right-title {
  font-size: 1.8dvw;
  font-weight: 700;
  letter-spacing: 0.15dvw;
  text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
  background: linear-gradient(135deg, #ffffff 0%, #e0f7ff 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
  z-index: 3;
  margin-bottom: 2dvw;
}

/* 右侧功能标签已移至左侧，此处样式已移除 */

.login-phone-group {
  width: 65%;
  display: flex;
  align-items: center;
  position: relative;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  overflow: hidden;
}

.login-phone-group:focus-within {
  border-color: rgba(0, 255, 255, 0.6);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.2),
    0 0 20px rgba(0, 255, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.login-phone-select {
  width: 25%;
  height: 100%;
}

:deep(.login-phone-select > div:first-child) {
  height: 100%;
  border-radius: 12px 0 0 12px;
}

.login-phone-input {
  width: 75%;
  padding-block: 0.2dvw;
}

/* Enhanced Element Plus input styling */
.login-phone-group :deep(.el-input__wrapper) {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  border-radius: 0 !important;
}

.login-phone-group :deep(.el-input__inner) {
  background: transparent !important;
  border: none !important;
  color: white !important;
  font-weight: 500;
  letter-spacing: 0.05dvw;
  font-size: 0.8dvw;
}

.login-phone-group :deep(.el-input__inner::placeholder) {
  color: rgba(255, 255, 255, 0.6) !important;
  font-weight: 400;
}

.login-phone-group :deep(.el-select .el-input__wrapper) {
  background: transparent !important;
  border-right: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.login-phone-area-code {
  float: left;
  margin-right: 1.5dvw;
}

.login-phone-area-place {
  float: right;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.7dvw;
}

.login-captcha-group {
  display: flex;
  width: 65%;
  position: relative;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  overflow: hidden;
}

.login-captcha-group:focus-within {
  border-color: rgba(0, 255, 255, 0.6);
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.2),
    0 0 20px rgba(0, 255, 255, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.login-captcha-input {
  width: 65%;
  padding-block: 0.2dvw;
}

.login-captcha-group :deep(.el-input__wrapper) {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  border-radius: 0 !important;
}

.login-captcha-group :deep(.el-input__inner) {
  background: transparent !important;
  border: none !important;
  color: white !important;
  font-weight: 500;
  letter-spacing: 0.05dvw;
  font-size: 0.8dvw;
}

.login-captcha-group :deep(.el-input__inner::placeholder) {
  color: rgba(255, 255, 255, 0.6) !important;
  font-weight: 400;
}

.login-captcha-button {
  width: 35%;
  background: linear-gradient(135deg, #87ceeb 0%, #6097fc 100%);
  color: white;
  border: none;
  border-radius: 0 12px 12px 0;
  font-size: 0.7dvw;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  letter-spacing: 0.05dvw;
  box-shadow: 0 2px 8px rgba(135, 206, 235, 0.3);
}

.login-captcha-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left 0.6s;
}

.login-captcha-button::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.3) 0%,
    transparent 70%
  );
  transform: translate(-50%, -50%);
  transition: all 0.4s ease;
  border-radius: 50%;
}

.login-captcha-button:hover:not(.disabled) {
  background: linear-gradient(135deg, #a0d8f0 0%, #80b3ff 100%);
  box-shadow: 0 4px 12px rgba(135, 206, 235, 0.4);
}

.login-captcha-button:hover:not(.disabled)::before {
  left: 100%;
}

.login-captcha-button:hover:not(.disabled)::after {
  width: 100px;
  height: 100px;
}

.login-captcha-button.disabled {
  background: linear-gradient(
    135deg,
    rgba(135, 206, 235, 0.3) 0%,
    rgba(96, 151, 252, 0.3) 100%
  );
  cursor: not-allowed;
  opacity: 0.6;
  box-shadow: none;
}

.login-captcha-button:active:not(.disabled) {
  background: linear-gradient(135deg, #6bb6e8 0%, #4080ff 100%);
  color: white;
}

/* 登录按钮样式 - 渐变蓝色设计 */
.login-button {
  width: 65%;
  height: 4.5dvh;
  background: linear-gradient(135deg, #87ceeb 0%, #6097fc 100%);
  color: white;
  border: none;
  border-radius: 30px;
  font-size: 1.1dvw;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  letter-spacing: 0.2dvw;
  text-transform: uppercase;
}

.login-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left 0.6s ease;
}

.login-button::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.4) 0%,
    transparent 70%
  );
  transform: translate(-50%, -50%);
  transition: all 0.4s ease;
  border-radius: 50%;
}

.login-button:hover:not(.disabled)::before {
  left: 100%;
}

.login-button:hover:not(.disabled)::after {
  width: 200px;
  height: 200px;
}

.login-button:hover:not(.disabled) {
  background: linear-gradient(135deg, #a0d8f0 0%, #80b3ff 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(135, 206, 235, 0.4);
}

.login-button.disabled {
  background: linear-gradient(
    135deg,
    rgba(135, 206, 235, 0.3) 0%,
    rgba(96, 151, 252, 0.3) 100%
  );
  cursor: not-allowed;
  opacity: 0.6;
  box-shadow: none;
}

.login-button:active:not(.disabled) {
  background: linear-gradient(135deg, #6bb6e8 0%, #4080ff 100%);
  transform: translateY(0);
}

/* 复选框组样式 - Simplified Design */
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 0.6dvw;
  margin: 0.8dvw 0;
}

/* 记住登录复选框样式 */
.remember-checkbox {
  color: white;
  font-size: 0.8dvw;
  font-weight: 400;
  letter-spacing: 0.02dvw;
}

.remember-checkbox :deep(.el-checkbox__label) {
  color: white;
  font-size: 0.8dvw;
  font-weight: 400;
  letter-spacing: 0.02dvw;
}

.remember-checkbox :deep(.el-checkbox__input .el-checkbox__inner) {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.remember-checkbox :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background: linear-gradient(135deg, #0057ff 0%, #00d4ff 100%);
  border-color: #00d4ff;
  box-shadow: 0 0 15px rgba(0, 212, 255, 0.4);
}

/* 用户协议复选框样式 */
.policy-checkbox {
  color: white;
  font-size: 0.8dvw;
  font-weight: 400;
  letter-spacing: 0.02dvw;
}

.policy-checkbox :deep(.el-checkbox__label) {
  color: white;
  font-size: 0.8dvw;
  font-weight: 400;
  letter-spacing: 0.02dvw;
}

.policy-checkbox :deep(.el-checkbox__input .el-checkbox__inner) {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.policy-checkbox :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background: linear-gradient(135deg, #0057ff 0%, #00d4ff 100%);
  border-color: #00d4ff;
  box-shadow: 0 0 15px rgba(0, 212, 255, 0.4);
}

/* 用户协议链接样式 - Enhanced Futuristic Design */
.policy-link {
  color: #00d4ff;
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  padding: 0 2px;
  border-radius: 3px;
  background: linear-gradient(
    135deg,
    transparent 0%,
    rgba(0, 212, 255, 0.1) 100%
  );
}

.policy-link::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 1px;
  background: linear-gradient(90deg, #00d4ff 0%, #0057ff 100%);
  transition: width 0.3s ease;
}

.policy-link:hover {
  color: #ffffff;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.6);
  background: linear-gradient(
    135deg,
    rgba(0, 212, 255, 0.2) 0%,
    rgba(0, 87, 255, 0.1) 100%
  );
  transform: translateY(-1px);
}

.policy-link:hover::before {
  width: 100%;
}

/* Enhanced Left Side Content Styling - Removed Animations */

/* Enhanced Element Plus Select Dropdown Styling */
:deep(.el-select-dropdown) {
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: blur(20px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  border-radius: 12px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3) !important;
}

:deep(.el-select-dropdown .el-select-dropdown__item) {
  color: white !important;
  background: transparent !important;
  transition: all 0.3s ease !important;
}

:deep(.el-select-dropdown .el-select-dropdown__item:hover) {
  background: rgba(0, 255, 255, 0.2) !important;
  color: white !important;
}

:deep(.el-select-dropdown .el-select-dropdown__item.selected) {
  background: rgba(0, 87, 255, 0.3) !important;
  color: white !important;
}

/* Enhanced Element Plus Select Input Styling */
.login-phone-group :deep(.el-select .el-input .el-input__wrapper) {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

.login-phone-group :deep(.el-select .el-input .el-input__inner) {
  color: white !important;
  background: transparent !important;
  font-weight: 500;
  letter-spacing: 0.05dvw;
}

.login-phone-group :deep(.el-select .el-input .el-input__suffix) {
  color: rgba(255, 255, 255, 0.7) !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-content {
    flex-direction: column;
    height: auto;
    min-height: 80dvh;
    padding: 2dvh;
  }

  .login-content-left {
    margin-bottom: 2dvh;
    padding: 2dvh;
  }

  .left-content-container {
    gap: 3dvh;
  }

  .logo-section {
    flex-direction: column;
    gap: 2dvh;
  }

  .feature-icons-left,
  .feature-icons-right {
    flex-direction: row;
    gap: 3dvw;
  }

  .floating-icon {
    --icon-size: 6dvw;
  }

  .floating-icon span {
    font-size: 3dvw;
  }

  .centered-logo {
    width: 15dvw;
  }

  .app-title {
    font-size: 5dvw;
    margin-bottom: 2dvh;
  }

  .app-description {
    font-size: 3dvw;
    max-width: 95%;
  }

  .feature-category {
    padding: 2dvh;
  }

  .category-title {
    font-size: 3.5dvw;
    margin-bottom: 2dvh;
  }

  .feature-item {
    padding: 1.5dvh 2.5dvh;
  }

  .feature-icon {
    font-size: 3dvw;
  }

  .feature-text {
    font-size: 2.5dvw;
  }

  .login-content-right {
    width: 100%;
    padding: 3dvh 2dvw;
  }

  .login-content-right-title {
    font-size: 4dvw;
  }

  /* 右侧功能标签已移至左侧，移动端样式已移除 */

  .login-phone-group,
  .login-captcha-group {
    width: 90%;
  }

  .login-captcha-button {
    font-size: 2dvw;
  }

  .login-button {
    width: 90%;
    height: 6dvh;
    font-size: 3dvw;
  }

  .checkbox-group {
    gap: 1dvh;
  }

  .remember-checkbox {
    font-size: 2.1dvw;
  }

  .remember-checkbox :deep(.el-checkbox__label) {
    font-size: 2.1dvw;
  }

  .policy-checkbox {
    font-size: 2.1dvw;
  }

  .policy-checkbox :deep(.el-checkbox__label) {
    font-size: 2.1dvw;
  }
}
