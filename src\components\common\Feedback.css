﻿.feedback-dialog :deep(.el-dialog) {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.95) 0%, rgba(135, 206, 235, 0.05) 100%);
  backdrop-filter: blur(25px);
  border-radius: 1dvw;
  border: 1px solid rgba(135, 206, 235, 0.3);
  box-shadow: 0 8px 32px rgba(96, 151, 252, 0.15);
}

.feedback-dialog :deep(.el-dialog__header) {
  padding: 1.5dvw 2dvw 1dvw;
  border-bottom: 1px solid rgba(135, 206, 235, 0.2);
}

.feedback-dialog :deep(.el-dialog__title) {
  font-size: 1.2dvw;
  font-weight: 600;
  color: #6097fc;
}

.feedback-dialog :deep(.el-dialog__body) {
  padding: 1.5dvw 2dvw;
}

/* 表单样式 */
.feedback-form {
  display: flex;
  flex-direction: column;
  gap: 1.5dvw;
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 0.8dvw;
}

.section-title {
  font-size: 1dvw;
  font-weight: 600;
  color: #333;
  margin: 0;
}

/* 反馈类型选择 */
.feedback-types {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.8dvw;
}

.feedback-type-item {
  padding: 0.8dvw 1.2dvw;
  border: 1px solid rgba(135, 206, 235, 0.3);
  border-radius: 0.4dvw;
  text-align: center;
  font-size: 0.9dvw;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(135, 206, 235, 0.05) 100%);
  backdrop-filter: blur(5px);
}

.feedback-type-item:hover {
  border-color: #6097fc;
  color: #6097fc;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(96, 151, 252, 0.15);
}

.feedback-type-item.active {
  background: linear-gradient(135deg, #6097fc 0%, #87ceeb 100%);
  border-color: #6097fc;
  color: white;
  box-shadow: 0 4px 12px rgba(96, 151, 252, 0.3);
}

/* 输入框样式 */
.feedback-textarea :deep(.el-textarea__inner) {
  border-radius: 0.4dvw;
  border: 1px solid rgba(135, 206, 235, 0.3);
  font-size: 0.9dvw;
  line-height: 1.4;
  resize: none;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(135, 206, 235, 0.02) 100%);
}

.feedback-textarea :deep(.el-textarea__inner):focus {
  border-color: #6097fc;
  box-shadow: 0 0 0 2px rgba(96, 151, 252, 0.15);
}

.contact-input {
  margin-bottom: 0.8dvw;
}

.contact-input :deep(.el-input__wrapper) {
  border-radius: 0.4dvw;
  border: 1px solid rgba(135, 206, 235, 0.3);
  transition: all 0.3s ease;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(135, 206, 235, 0.02) 100%);
}

.contact-input :deep(.el-input__wrapper):hover {
  border-color: #6097fc;
}

.contact-input :deep(.el-input__wrapper.is-focus) {
  border-color: #6097fc;
  box-shadow: 0 0 0 2px rgba(96, 151, 252, 0.15);
}

.contact-input :deep(.el-input__inner) {
  font-size: 0.9dvw;
  padding-left: 0.5dvw;
}

.contact-input :deep(.el-input__prefix) {
  color: #999;
}

/* 表单项样式 */
.feedback-form :deep(.el-form-item) {
  margin-bottom: 0;
}

.feedback-form :deep(.el-form-item__error) {
  font-size: 0.8dvw;
  margin-top: 0.3dvw;
}

/* 底部按钮 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1dvw;
  padding: 1dvw 0 0;
  border-top: 1px solid rgba(135, 206, 235, 0.2);
}

.cancel-button {
  padding: 0.6dvw 1.5dvw;
  font-size: 0.9dvw;
  border-radius: 0.4dvw;
  border: 1px solid rgba(135, 206, 235, 0.3);
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(135, 206, 235, 0.05) 100%);
  color: #666;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.cancel-button:hover {
  border-color: #6097fc;
  color: #6097fc;
  box-shadow: 0 2px 6px rgba(96, 151, 252, 0.1);
}

.submit-button {
  padding: 0.6dvw 1.5dvw;
  font-size: 0.9dvw;
  border-radius: 0.4dvw;
  background: linear-gradient(135deg, #6097fc 0%, #87ceeb 100%);
  border: none;
  color: white;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(96, 151, 252, 0.2);
}

.submit-button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.submit-button:hover:not(.is-loading)::before {
  left: 100%;
}

.submit-button:hover:not(.is-loading) {
  background: linear-gradient(135deg, #87ceeb 0%, #6097fc 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(96, 151, 252, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .feedback-dialog :deep(.el-dialog) {
    width: 90dvw !important;
    margin: 5dvh auto;
  }

  .feedback-dialog :deep(.el-dialog__title) {
    font-size: 4dvw;
  }

  .section-title {
    font-size: 3.5dvw;
  }

  .feedback-types {
    grid-template-columns: 1fr;
    gap: 1.5dvh;
  }

  .feedback-type-item {
    padding: 2dvh 3dvw;
    font-size: 3.5dvw;
  }

  .feedback-textarea :deep(.el-textarea__inner) {
    font-size: 3.5dvw;
  }

  .contact-input :deep(.el-input__inner) {
    font-size: 3.5dvw;
  }

  .cancel-button,
  .submit-button {
    padding: 2dvh 4dvw;
    font-size: 3.5dvw;
  }

  .dialog-footer {
    gap: 3dvw;
  }
}
