
class NlsClient {
    constructor(config) {
        if (!config || !config.domain || !config.appKey || !config.token) {
            throw new Error('invalid config!');
        }
        this._config = config;
        this._ws = null;  // WebSocket 实例
    }

    start(onmessage, onclose) {
        const {
            domain, appKey, token
        } = this._config;

        // const url = `wss://${domain}/ws/v1?appkey=${encodeURIComponent(appKey)}&token=${encodeURIComponent(token)}`;
        const url = 'wss://nls-gateway.cn-shanghai.aliyuncs.com/ws/v1?token=' + token; // this._config.domain'
        this._ws = new WebSocket(url);

        this._ws.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                onmessage?.(data);
            }
            catch (e) {
                console.warn('[NLS] Failed to parse message:', event.data);
                console.log(e);
            }
        };

        this._ws.onclose = (event) => {
            console.log('[NLS] WebSocket closed:', event.code, event.reason);
            onclose?.(event);
        };
        return new Promise((resolve, reject) => {
            this._ws.onopen = () => {
                console.log('[NLS] WebSocket connection opened.');
                resolve();
            };
            this._ws.onerror = (err) => {
                console.error('[NLS] WebSocket error:', err);
                reject(err);
            };
        });
    }

    sendBinaryMessage(buffer) {
        if (this._ws && this._ws.readyState === WebSocket.OPEN) {
            this._ws.send(buffer);
        }
        else {
            console.warn('[NLS] WebSocket is not open. Cannot send message.');
        }
    }

    sendMessage(data) {
        if (this._ws && this._ws.readyState === WebSocket.OPEN) {
            const json = typeof data === 'string' ? data : JSON.stringify(data);
            this._ws.send(json);
        }
        else {
            console.warn('[NLS] WebSocket is not open. Cannot send message.');
        }
    }

    shutdown() {
        if (this._ws) {
            this._ws.close(1000, 'Client shutdown');
            this._ws = null;
            console.log('[NLS] WebSocket connection shutdown.');
        }
    }

    defaultContext() {
        return {
            sdk: {
                name: 'nls-wx-sdk',
                version: '0.0.1',
                language: 'wxjs'
            }
        };
    }
}

export default NlsClient;
