.header-container {
  height: 6dvh;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  align-items: center;
}

.header-center {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
  margin: auto;
  height: 100%;
  height: 6dvh;
}

.header-right{
  display: flex;
  justify-content: right;
  padding-right: 24px;
}

.new-chat-button {
  font-size: 0.8dvw;
  display: flex;
  align-items: center;
  gap: 0.4dvw;
  padding: 0.6dvw 1.2dvw;
  color: white;
  background: linear-gradient(135deg, #87ceeb 0%, #6097fc 100%);
  border: none;
  border-radius: 0.4dvw;
  backdrop-filter: blur(15px);
  cursor: pointer;
  white-space: nowrap;
  line-height: 1;
  user-select: none;
  letter-spacing: 0.05dvw;
  transition: all 0.1s ease;
}

.new-chat-button:hover {
  background: linear-gradient(135deg, #a0d8f0 0%, #80b3ff 100%);
  border-color: #80b3ff;
  box-shadow: 0 4px 14px rgba(135, 206, 235, 0.4);
}

.new-chat-button:active {
  transform: scale(0.95);
}

.new-chat-button:focus-visible {
  outline: none;
  border-color: #6097fc;
  box-shadow:
    0 0 0 0.2dvw rgba(135, 206, 235, 0.35),
    0 4px 14px rgba(135, 206, 235, 0.3);
}
