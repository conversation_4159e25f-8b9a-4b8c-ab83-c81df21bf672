<template>
    <div>
        <div style="overflow-y: auto" :style="{height: props.height}">
            <component :is="props.component" />
        </div>
        <div class="el-dialog__footer" style="text-align: center">
            <el-button type="primary" @click="emit('close')">阅读并同意</el-button>
        </div>
    </div>
</template>

<script setup>
const emit = defineEmits(['close']);
const props = defineProps({
    component: {
        type: Object,
        required: true
    },
    height: {
        type: String,
        default: '500px'
    }
});
</script>

<style scoped lang="scss">

</style>
