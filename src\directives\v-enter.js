// v-enter.js
export default {
    mounted(el, binding) {
        let isComposing = false;

        const onCompositionStart = () => {
            isComposing = true;
        };
        const onCompositionEnd = () => {
            isComposing = false;
        };
        const onKeyUp = (e) => {
            if (e.shiftKey || e.ctrlKey || e.altKey) {
                return;
            }
            if (e.key === 'Enter' && !isComposing) {
                binding.value?.(e);
            }
        };

        el.__vEnterHandlers = {
            onCompositionStart,
            onCompositionEnd,
            onKeyUp,
        };

        el.addEventListener('compositionstart', onCompositionStart);
        el.addEventListener('compositionend', onCompositionEnd);
        el.addEventListener('keydown', onKeyUp);
    },
    unmounted(el) {
        const {
            onCompositionStart, onCompositionEnd, onKeyUp
        } = el.__vEnterHandlers || {};
        el.removeEventListener('compositionstart', onCompositionStart);
        el.removeEventListener('compositionend', onCompositionEnd);
        el.removeEventListener('keydown', onKeyUp);
        delete el.__vEnterHandlers;
    }
};
