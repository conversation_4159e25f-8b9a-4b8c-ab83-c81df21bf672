<template>
    <div class="vma-detail">
        <div class="header">
            <el-button type="primary" icon="Document" @click="saveMedicalRecord">保存病历</el-button>
        </div>
        <div class="body">
            <div class="page-container">
                <el-form :model="form" label-width="100px" label-position="top">
                    <rp-collapse title="基本信息" disabled>
                        <rp-grip-layout :columns="3" min-width="250px">
                            <el-form-item label="宠物名">
                                <el-input v-model="form.petName" placeholder="请输入宠物名" />
                            </el-form-item>
                            <el-form-item label="性别">
                                <el-radio-group v-model="form.petGender">
                                    <el-radio value="公">公</el-radio>
                                    <el-radio value="母">母</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item label="年龄">
                                <pet-birthday v-model="form.petBirthday" />
                            </el-form-item>
                            <el-form-item label="绝育状态">
                                <el-radio-group v-model="form.sterilization">
                                    <el-radio value="已绝育">已绝育</el-radio>
                                    <el-radio value="未绝育">未绝育</el-radio>
                                    <el-radio value="未知">未知</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item label="种类">
                                <el-radio-group v-model="form.petSpecies">
                                    <el-radio value="犬">犬</el-radio>
                                    <el-radio value="猫">猫</el-radio>
                                    <el-radio value="其他">其他</el-radio>
                                </el-radio-group>
                            </el-form-item>
                            <el-form-item label="品种">
                                <el-input v-model="form.petBreed" placeholder="请输入品种" />
                            </el-form-item>
                        </rp-grip-layout>
                    </rp-collapse>
                    <rp-collapse title="主观信息">
                        <el-form-item label="主诉">
                            <template #label>
                                <div class="form-label-copy">
                                    <div class="title">主诉</div>
                                    <rp-clipboard :text="form.chiefComplaint" />
                                </div>
                            </template>
                            <voice-input v-model="form.chiefComplaint" style="width: 100%;" />
                        </el-form-item>
                        <el-form-item label="既往病史">
                            <template #label>
                                <div class="form-label-copy">
                                    <div class="title">既往病史</div>
                                    <rp-clipboard :text="form.pastHistory" />
                                </div>
                            </template>
                            <voice-input v-model="form.pastHistory" type="textarea" />
                        </el-form-item>
                        <el-form-item label="现病史">
                            <template #label>
                                <div class="form-label-copy">
                                    <div class="title">现病史</div>
                                    <rp-clipboard :text="form.presentIllness" />
                                </div>
                            </template>
                            <voice-input v-model="form.presentIllness" type="textarea" />
                        </el-form-item>
                    </rp-collapse>
                    <rp-collapse title="体格检查">
                        <rp-grip-layout :columns="3" min-width="250px">
                            <el-form-item label="体重 (kg)">
                                <el-input v-model="form.petWeight" placeholder="请输入体重" />
                            </el-form-item>
                            <el-form-item label="体温 (°C)">
                                <el-input v-model="form.petTemperature" placeholder="请输入体温" />
                            </el-form-item>
                            <el-form-item label="心率 (次/分)">
                                <el-input v-model="form.petHeartRate" placeholder="请输入心率" />
                            </el-form-item>
                            <el-form-item label="呼吸频率 (次/分)">
                                <el-input v-model="form.petRespiratoryRate" placeholder="请输入呼吸频率" />
                            </el-form-item>
                        </rp-grip-layout>
                        <el-form-item label="体况描述">
                            <template #label>
                                <div class="form-label-copy">
                                    <div class="title">体况描述</div>
                                    <rp-clipboard :text="form.physicalExam" />
                                </div>
                            </template>
                            <voice-input
                                v-model="form.physicalExam" type="textarea"
                                placeholder="请填写体况描述"
                            />
                        </el-form-item>
                    </rp-collapse>
                    <rp-collapse title="客观检查">
                        <template #title>
                            客观检查
                            <small style="font-weight: 400; color: #666;">可上传检查检验报告、影像报告</small>
                        </template>
                        <h3>检查检验报告</h3>
                        <div class="lab-report">
                            <div v-for="(report, idx) in labReports" :key="report.id" class="lab-report-item">
                                <el-image
                                    style="width: 100px; height: 100px"
                                    :preview-src-list="labReports.map(item => item.url)"
                                    :initial-index="idx"
                                    show-progress
                                    hide-on-click-modal
                                    :src="report.url" fit="fill"
                                />
                                <el-popconfirm
                                    title="确定删除该报告吗？"
                                    confirm-button-text="确认"
                                    confirm-button-type="danger"
                                    cancel-button-text="取消"
                                    width="200px"
                                    @confirm="deleteLabReport(report, REPORT_TYPE_LABORATORY)"
                                >
                                    <template #reference>
                                        <rp-icon class="delete-btn" name="Delete" size="16" />
                                    </template>
                                </el-popconfirm>
                            </div>
                            <div class="upload-btn" @click="uploadLaboratoryReport">
                                <rp-icon name="upload" size="24" />
                                <div class="text">上传检查检验报告</div>
                            </div>
                        </div>
                        <h3>影像报告</h3>
                        <div class="lab-report">
                            <div v-for="(report, idx) in imageReports" :key="report.id" class="lab-report-item">
                                <el-image
                                    style="width: 100px; height: 100px"
                                    :preview-src-list="imageReports.map(item => item.url)"
                                    :initial-index="idx"
                                    show-progress
                                    hide-on-click-modal
                                    :src="report.url" fit="fill"
                                />
                                <el-popconfirm
                                    title="确定删除该报告吗？"
                                    confirm-button-text="确认"
                                    confirm-button-type="danger"
                                    cancel-button-text="取消"
                                    width="200px"
                                    @confirm="deleteLabReport(report, REPORT_TYPE_IMAGE)"
                                >
                                    <template #reference>
                                        <rp-icon class="delete-btn" name="Delete" size="16" />
                                    </template>
                                </el-popconfirm>
                            </div>
                            <div class="upload-btn" @click="uploadImageReport">
                                <rp-icon name="upload" size="24" />
                                <div class="text">上传影像报告</div>
                            </div>
                        </div>
                    </rp-collapse>
                    <rp-collapse title="诊断与治疗方案">
                        <el-form-item label="诊断">
                            <template #label>
                                <div class="form-label-copy">
                                    <div class="title">诊断</div>
                                    <rp-clipboard :text="form.diagnosis" />
                                </div>
                            </template>
                            <voice-input v-model="form.diagnosis" type="textarea" />
                        </el-form-item>
                        <el-form-item label="治疗方案">
                            <template #label>
                                <div class="form-label-copy">
                                    <div class="title">治疗方案</div>
                                    <rp-clipboard :text="form.treatment" />
                                </div>
                            </template>
                            <voice-input v-model="form.treatment" type="textarea" />
                        </el-form-item>
                        <el-form-item label="医嘱">
                            <template #label>
                                <div class="form-label-copy">
                                    <div class="title">医嘱</div>
                                    <rp-clipboard :text="form.medicalAdvice" />
                                </div>
                            </template>
                            <voice-input v-model="form.medicalAdvice" type="textarea" />
                        </el-form-item>
                    </rp-collapse>
                </el-form>
            </div>
            <div class="assistant-container">
                <div class="assistant-header">
                    <h2>AI辅助诊断</h2>
                    <div class="result-header">
                        <div class="avatar">
                            <img class="logo" src="../../assets/logo-white.png" alt="">
                        </div>
                        <div class="content">
                            <h3>AI分析结果</h3>
                            <p>基于主观信息、客观信息，进行辅助鉴别诊断</p>
                        </div>
                        <div class="action">
                            <div class="item" v-if="isAiGenerating" @click="stopGenerating">
                                <rp-icon class="icon" name="icon-circle-stop" size="22" />
                                停止分析
                            </div>
                            <div class="item" v-else @click="sendMessage2ChatModel">
                                <rp-icon class="icon" name="refresh" size="22" />
                                {{ chatMessages.length > 0 ? '重新分析' : '开始分析' }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="assistant-body">
                    <!--<chat-message-app v-if="chatMessage" :content="chatMessage.message" />-->
                    <chat-model
                        ref="chatModelRef"
                        :inputs="chatInputs"
                        :messages="chatMessages"
                        :base-url="httpApis.medicalRecordChat"
                    />
                </div>
                <div class="chat-input">
                    <chat-input
                        class="inputbox"
                        :settings="chatSettings"
                        :is-running="isAiGenerating"
                        @send-message="sendMessage"
                        @stop-message="stopGenerating"
                    >
                        <template #bottom>
                            <div class="chat-footer">
                                内容由AI生成，请仔细甄别
                            </div>
                        </template>
                    </chat-input>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed, nextTick, onMounted, reactive, ref, unref, watch } from 'vue';
import RpCollapse from '@/ui/layout/Collapse.vue';
import { http } from '@/extensions/http.js';
import { httpApis } from '@/hooks/useHttpApi.js';
import { useToast } from '@/hooks/useToast.js';
import PetBirthday from '@/components/his/PetBirthday.vue';
import RpClipboard from '@/ui/clipboard/Clipboard.vue';
import RpGripLayout from '@/ui/layout/GridLayout.vue';
import ChatModel from '@/components/open-ai/ChatModel.vue';
import RpIcon from '@/ui/icon/icon.vue';
import ChatInput from '@/views/chat/widgets/ChatInput.vue';
import VoiceInput from '@/ui/form/VoiceInput.vue';
import { useShowDialog } from '@/ui/index.js';
import LaboratoryOcrView from '@/views/chat/widgets/laboratory/ocr.vue';
import ImageReportOcrView from '@/views/chat/widgets/image/ocr.vue';
import { groupBy } from 'lodash';

const isAiGenerating = ref(false);
const chatModelRef = ref(null);
const chatMessages = ref([]);
const chatInputs = ref({
    id: ''
});
const labReports = reactive([]);
const imageReports = reactive([]);
const REPORT_TYPE_LABORATORY = 1;
const REPORT_TYPE_IMAGE = 2;
const props = defineProps({
    id: {
        type: String,
        required: true
    }
});

const chatSettings = {
    rows: 1,
    tools: {
        // 化验图片
        laboratory: false,
        // 影像图片
        image: false,
    }
};

const form = ref({
    petName: '',
    petGender: '',
    petBirthday: '',
    sterilization: '',
    petSpecies: '',
    petBreed: '',
    visitDate: '',
    chiefComplaint: '',
    pastHistory: '',
    presentIllness: '',
    petWeight: '',
    petTemperature: '',
    petHeartRate: '',
    petRespiratoryRate: '',
    diagnosis: '',
    differentialDiagnosis: '',
    treatment: '',
    examFindings: '',
    physicalExam: '',
    medicalAdvice: ''
});
onMounted(() => {
    chatInputs.value.id = props.id;
    initRecord().then(() => {
        if (chatMessages.value.length === 0) {
            const medical = unref(form);
            if (medical.petSpecies && medical.chiefComplaint) {
                sendMessage2ChatModel();
            }
        }
    });
});
watch(() => props.id, async (newVal) => {
    if (isAiGenerating.value) {
        stopGenerating();
    }
    chatInputs.value.id = props.id;
    await initRecord();
});
const initRecord = async () => {
    await getMedicalChatMessages(props.id).then(data => {
        if (data && data.length > 0) {
            let list = [];
            data.forEach(msg => {
                if (msg.message.indexOf('</think>') !== -1) {
                    msg.message = '<think>' + msg.message;
                }
                list.push({
                    inputs: {
                        id: msg.medicalId,
                    },
                    query: msg.query,
                    answer: msg.message,
                    messageId: msg.messageId,
                    rating: msg.rating,
                    content: msg.content,
                    status: 1
                });
            });
            chatMessages.value.splice(0, chatMessages.value.length, ...list);
        }
        else {
            chatMessages.value.splice(0);
        }
    });
    await getMedicalRecord(props.id).then(res => {
        form.value = res;
    });
    labReports.splice(0);
    imageReports.splice(0);
    getLabReports(props.id).then(data => {
        if (data && data.length > 0) {
            const map = groupBy(data, 'type');
            Object.keys(map).forEach(type => {
                const items = map[type];
                if (+type === REPORT_TYPE_IMAGE) {
                    imageReports.push(...items);
                }
                else if (+type === REPORT_TYPE_LABORATORY) {
                    labReports.push(...items);
                }
            });
        }
    });
};
const getMedicalRecord = async (id) => {
    return http.get(httpApis.getMedicalRecord, {
        params: {
            id: id
        }
    }).then(res => res.data);
};
const saveMedicalRecord = async () => {
    return http.post(httpApis.updateMedicalRecord, {
        ...form.value
    }).then(res => {
        useToast({
            message: '保存成功',
            type: 'success'
        });
    });
};
const getMedicalChatMessages = async (id) => {
    return http.get(httpApis.getMedicalRecordChatMessages, {
        rest: {
            medicalId: id
        }
    }).then(res => res.data);
};
const getLabReports = async (id) => {
    return http.get(httpApis.getLabReportByMedicalRecord, {
        params: {
            medicalRecordId: id
        }
    }).then(res => res.data);
};
/**
 * 上传化验结果
 */
const uploadLaboratoryReport = () => {
    useShowDialog(LaboratoryOcrView, {
        resultType: 'json',
        uploadType: 'url'
    }, {
        title: '上传化验结果',
        width: '880px',
        closeOnPressEscape: false
    }).then(async (data) => {
        for (let i = 0; i < data.length; i++) {
            await createLabReport(data[i], REPORT_TYPE_LABORATORY).then(res => {
                console.log('createLabReport', res);
                labReports.push(res);
            });
        }
    });
};
const uploadImageReport = () => {
    useShowDialog(ImageReportOcrView, {
        resultType: 'json',
        uploadType: 'url'
    }, {
        title: '上传影像报告',
        width: '880px',
        closeOnPressEscape: false
    }).then(async (data) => {
        for (let i = 0; i < data.length; i++) {
            await createLabReport(data[i], REPORT_TYPE_IMAGE).then(res => {
                console.log('createLabReport', res);
                imageReports.push(res);
            });
        }
    });
};
/**
 * 删除化验结果
 */
const deleteLabReport = (report, type) => {
    return http.delete(httpApis.deleteLabReport, {
        rest: {
            id: report.id
        }
    }).then(res => {
        if (type === REPORT_TYPE_IMAGE) {
            imageReports.splice(imageReports.indexOf(report), 1);
        }
        else if (type === REPORT_TYPE_LABORATORY) {
            labReports.splice(labReports.indexOf(report), 1);
        }
        useToast({
            message: '删除成功',
            type: 'success'
        });
    });
};
const createLabReport = (report, type) => {
    const checkResult = {};
    if (type === REPORT_TYPE_LABORATORY) {
        checkResult.items = report.items;
        checkResult.pet = report.pet;
        checkResult.remark = report.remark;
    }
    else if (type === REPORT_TYPE_IMAGE) {
        checkResult.pet = report.pet;
        checkResult.reportProjectName = report.reportProjectName;
        checkResult.checkLocation = report.checkLocation;
        checkResult.examinationFindings = report.examinationFindings;
        checkResult.conclusion = report.conclusion;
        checkResult.checkDate = report.checkDate;
    }
    return http.post(httpApis.createLabReport, {
        medicalRecordId: props.id,
        url: report.image,
        checkResult: JSON.stringify(checkResult),
        type: type
    }).then(res => res.data);
};
const sendMessage = (inputMessage) => {
    if (isAiGenerating.value) {
        return;
    }
    nextTick(() => {
        isAiGenerating.value = true;
        chatModelRef.value.sendMessage(inputMessage).finally(() => {
            isAiGenerating.value = false;
        });
    });
};
const sendMessage2ChatModel  = async () => {
    if (isAiGenerating.value) {
        return;
    }
    if (!form.value.petSpecies || !form.value.chiefComplaint) {
        useToast({
            message: '请先完善一下宠物种类和主诉信息',
            type: 'error'
        });
        return;
    }
    await saveMedicalRecord();
    await nextTick();
    const message = '鉴别诊断';
    isAiGenerating.value = true;
    const forget = chatMessages.value.length > 0;
    chatMessages.value.splice(0);
    chatModelRef.value.sendMessage(message, {
        forget: forget
    }).finally(() => {
        console.log('chat finish');
        isAiGenerating.value = false;
    });
};
const stopGenerating = () => {
    chatModelRef.value.stopGenerating();
};
</script>

<style lang="scss" scoped>
.vma-detail {
    position: relative;
    .header {
        background: linear-gradient(180deg, #f2f2f2, #ffffff);
        box-shadow: 0 10px 10px #fff;
        padding: 10px 20px;
        position: sticky;
        top: 0;
        z-index: 100;
        height: 55px;
    }
}
.body {
    display: flex;
}
.page-container {
    flex: 1;
    padding: 20px;
    max-width: 1100px;
    margin: 0 auto;
    .el-form-item {
        :deep(.el-form-item__label) {
            height: 24px;
            line-height: 24px;
        }
        .clipboard {
            display: none;
        }
        &:hover {
            .clipboard {
                display: block;
            }
        }
    }
}
.lab-report {
    display: grid;
    grid-template-columns: repeat(auto-fill, 100px);
    gap: 10px;
}
.lab-report-item {
    position: relative;
    text-align: center;
    :deep(.el-image) {
        display: block;
        border-radius: 5px;
        margin: 0 auto;
    }
    .delete-btn {
        display: none;
        cursor: pointer;
        position: absolute;
        bottom: 5px;
        left: 50%;
        margin-left: -8px;
        background: rgba(0, 0, 0, .5);
        color: #fff;
        border-radius: 50%;
        width: 1.3em;
        height: 1.3em;
    }
    &:hover {
        .delete-btn {
            display: flex;
        }
    }
}
.upload-btn {
    border: 1px dashed #ddd;
    border-radius: 5px;
    text-align: center;
    padding: 0;
    width: 100px;
    height: 100px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    &:hover {
        background: #f8f8f8;
    }
    .text {
        font-size: 12px;
        color: #888;
    }
}
.assistant-container {
    width: 400px;
    padding: 0 20px 0 0;
    height: calc(100vh - 55px);
    overflow-y: auto;
    position: sticky;
    top: 55px;
    display: flex;
    flex-direction: column;
    .assistant-header {
        position: sticky;
        top:0;
        background: #fff;
        z-index: 1;
        box-shadow: 0 15px 15px #ffffff;
        margin-bottom: 20px;
    }
    h2 {
        border-bottom: 1px solid #ddd;
        padding-top: 20px;
    }
    .result-header {
        display: flex;
        align-items: center;
        .content {
            margin-left: 5px;
            flex: 1;
            p {
                font-size: 12px;
                color: #999;
            }
        }
        .item {
            cursor: pointer;
            color: var(--el-color-primary);
        }
        .avatar {
            width: 50px;
            line-height: 1;
        }
        .logo {
            width: 44px;
            border-radius: 50%;
            margin: 3px;
        }
    }
    .assistant-body {
        flex: 1;
    }
    .chat-input {
        position: sticky;
        bottom: 0;
        background: #fff;
        box-shadow: 0 -15px 15px #ffffff;
        margin-top: 20px;
        .chat-footer {
            text-align: center;
            color: #999;
            font-size: 12px;
            margin: 10px 0;
        }
        :deep(.laboratory-values) {
            display: none;
        }
    }
}
:deep(.el-form-item__label) {
    display: block;
}
.form-label-copy {
    display: flex;
    .title {
        //flex: 1;
        //width: 100%;
        margin-right: 10px;
    }
}
.is-mobile {
    .vma-detail {
        margin-top: 50px;
        margin-bottom: 40px;
        .header {
            position: fixed;
            width: 100%;
            top: auto;
            bottom: 0;
            border: none;
            background: #fff;
            .el-button {
                width: 100%;
            }
        }
    }
    .body {
        display: block;
        .assistant-container {
            width: 100%;
            height: auto;
            padding: 20px;
            h2 {
                padding: 0;
            }
        }
    }
}
</style>
