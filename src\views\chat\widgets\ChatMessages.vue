<template>
    <div class="chat-messages" ref="messagesContainer">
        <div
            v-for="(message, index) in messages"
            :key="index"
        >
            <chat-message :message="message" />
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue';
import ChatMessage from './ChatMessage.vue';

const props = defineProps({
    messages: {
        type: Array,
        required: true
    }
});
const messagesContainer = ref(null);

</script>

<style lang="scss" scoped>
.chat-messages {
    height: 100%;
    flex: 1;
    overflow-y: auto;
    padding: 0;

    .empty-state {
        text-align: center;
        padding: 40px;
        color: #666;

        h2 {
            margin-bottom: 16px;
        }
    }

    .message {
        margin-bottom: 16px;
        display: flex;

        &.user {
            justify-content: flex-end;

            .message-content {
                background: var(--el-color-primary);
                color: white;
            }
        }

        .message-content {
            max-width: 80%;
            padding: 12px 16px;
            background: #f4f4f5;
            border-radius: 8px;
        }
    }
}

.placeholder-text {
    color: #999;
    font-size: 14px;
}
</style>
