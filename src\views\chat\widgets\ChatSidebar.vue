<template>
    <el-aside width="260px" class="chat-aside">
        <div class="aside-header">
            <div class="logo-box" @click="router.push({path: '/'})">
                <img src="@/assets/logo-white.png" class="logo" alt="logo">
                <h1>好兽医AI助手</h1>
            </div>
            <div class="collapse">
                <el-tooltip effect="dark" content="收起边栏" placement="right" :disabled="env.isMobile.value">
                    <rp-icon name="icon-sidebar-left" @click="closeSidebar" />
                </el-tooltip>
            </div>
        </div>
        <div class="open-collapse">
            <el-tooltip effect="dark" content="打开边栏" placement="right">
                <rp-icon name="icon-sidebar-right" @click="openSidebar" />
            </el-tooltip>
        </div>

        <div class="new-chat-btn">
            <el-button class="btn-has-text" type="primary" @click="openNewChat" round block>
                <rp-icon name="icon-new-chat" size="16" />
                <span style="margin-left: 5px;">开启新对话</span>
            </el-button>
            <el-tooltip effect="dark" content="开启新对话" placement="right">
                <rp-icon class="btn-simple" name="icon-new-chat" @click="openNewChat" />
            </el-tooltip>
        </div>
        <global-navigator-pc :is-sidebar-collapsed="isSidebarCollapsed" />
        <chat-history @change="handleConversationChange" />
        <user-center v-if="userStore.isLogin" />
    </el-aside>
</template>

<script setup>
import { useRouter } from 'vue-router';
import ChatHistory from './ChatHistory.vue';
import UserCenter from './UserCenter.vue';
import { useUserStore } from '@/stores/user';
import { useShowDrawer } from '@/ui/index.js';
import ChatBox from '@/views/chat/widgets/ChatBox.vue';
import { useEventBus } from '@vueuse/core';
import { onMounted, ref } from 'vue';
import { useEnv } from '@/hooks/useEnv';
import GlobalNavigatorPc from '@/components/common/GlobalNavigatorPc.vue';
const env = useEnv();
const userStore = useUserStore();
const knowledgeAssistant = ref(null);
const bus = useEventBus(Symbol('chat'));
bus.on((event, payload) => {
    if (event === 'newChat') {
        knowledgeAssistant.value = payload;
    }
});
const router = useRouter();
const props = defineProps({
    isSidebarCollapsed: {
        type: Boolean,
        default: false
    }
});
const emit = defineEmits(['update:isSidebarCollapsed']);
onMounted(() => {
    if (env.isMobile.value) {
        closeSidebar();
    }
});
const openSidebar = () => {
    emit('update:isSidebarCollapsed', false);
};
const closeSidebar = () => {
    emit('update:isSidebarCollapsed', true);
};
const openNewChat = () => {
    router.push({
        name: 'ChatNew',
        query: {
            t: Date.now()
        }
    });
};
const handleConversationChange = (row) => {
    router.push({
        name: 'ChatDetail',
        params: {
            id: row.id
        }
    });
    if (env.isMobile.value) {
        closeSidebar();
    }
};
const showKnowledgeAssistant = () => {
    useShowDrawer(ChatBox, {
        id: knowledgeAssistant.value ? knowledgeAssistant.value.id : '',
        scene: 'knowledge',
        title: '我是知识查询助手，需要我帮你查什么呢？',
        subtitle: '关于疾病、药品相关的知识都可以问我哦~',
        eventBus: bus,
        settings: {
            rows: 1,
            tools: {
                // 化验图片
                laboratory: false,
                // 影像图片
                image: false,
            }
        }
    }, {
        title: '知识查询',
        width: '600px',
        bodyClass: 'rp-dialog-body-chatbox',
        direction: 'ltr'
    }).catch(e => {
    }).finally(() => {
        bus.reset();
    });
};
</script>

<style lang="scss" scoped>
.chat-aside {
    background: #f9fbff;
    transition: width 0.3s ease-out;
    display: flex;
    flex-direction: column;
    border-right: 1px solid var(--el-border-color);
    .aside-header {
        height: 50px;
        display: flex;
        padding: 0 8px;
        align-items: center;
        .logo-box {
            cursor: pointer;
            display: flex;
            align-items: center;
            flex: 1;
            color: #0068b7;
            .logo {
                height: 32px;
                border-radius: 5px;
            }
            h1 {
                flex: 1;
                white-space: nowrap;
                margin-left: 5px;
            }
        }
        .collapse {
            width: 30px;
            cursor: pointer;
            text-align: right;
        }
    }
    .open-collapse {
        display: none;
    }

    .new-chat-btn {
        padding: 10px 16px;
        .btn-simple {
            display: none;
        }
        .btn-has-text {
            display: block;
        }
    }
}
.is-sidebar-collapsed {
    .chat-aside {
        width: 50px !important;
    }
    .aside-header {
        .collapse {
            display: none;
        }
    }
    .open-collapse {
        display: block !important;
        padding: 10px 16px;
        cursor: pointer;
    }
    .new-chat-btn {
        padding: 10px 16px !important;
        .btn-simple {
            display: block !important;
            margin-left: 0;
            cursor: pointer;
        }
        .btn-has-text {
            display: none !important;
        }
    }
    .chat-history {
        display: none;
    }
    .logo-box {
        h1 {
            display: none;
        }
    }
    .user-center {
        :deep(.uc span) {
            display: none;
        }
    }
    .iconfont {
        font-size: 18px;
    }
}
.is-mobile {
    .is-sidebar-collapsed {
        .chat-aside {
            display: none;
        }
    }
    .chat-aside {
        width: 100%;
        position: relative;
        z-index: 101;
    }
    .new-chat-btn {
        display: none;
    }
}
</style>
