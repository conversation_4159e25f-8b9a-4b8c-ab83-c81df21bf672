<script lang="ts" setup>
import StopIcon from "@/components/icons/StopIcon.vue";
import SubmitArrowIcon from "@/components/icons/SubmitArrowIcon.vue";
import { type ChatMessage } from '@/services/chatService';
import KnowledgeService, { type BookInfo } from '@/services/knowledgeService';
import { useChatStore } from '@/stores/chat';
import { ElMessage } from 'element-plus';
import { storeToRefs } from "pinia";
import { ref } from 'vue';

// 全局Pina库
const chatStore = useChatStore();

// 全局响应数据
const {
    messageList,
    isAiTyping
} = storeToRefs(chatStore);

// 本组件的响应数据
const senderValue = ref("");

// 本组件的方法
function addErrorMessage(msg: string = "输出已中断，请重新发送消息") {
    const lastAiMessage = messageList.value
        .slice()
        .reverse()
        .find((msg) => msg.type === "ai");
    if (lastAiMessage) {
        lastAiMessage.loading = false;
        lastAiMessage.thinkLoading = false;
        if (!lastAiMessage.content) {
            lastAiMessage.content = msg;
        }
    }
}

// 本组件的方法
const addMessage = (
    messageId: number,
    message: string,
    type: "user" | "ai" = "user",
) => {
    const newMessage: ChatMessage = {
        id: messageId,
        type,
        content: message,
        timestamp: Date.now(),
        loading: type === "ai",
        thinkLoading: type === "ai",
        books: [],
    };

    messageList.value.push(newMessage);
    return newMessage;
};

const updateMessage = (
    messageId: number,
    content: string,
    thinking?: string,
    finished?: boolean,
    thinkFinished?: boolean,
    books?: BookInfo[]
) => {
    const messageIndex = messageList.value.findIndex(
        (msg) => msg.id === messageId
    );
    if (messageIndex !== -1) {
        const message = messageList.value[messageIndex];

        // 批量更新消息属性以减少响应式触发次数
        const updates: Partial<typeof message> = {};

        if (content !== message.content) {
            updates.content = content;
        }
        if (thinking !== undefined && thinking !== message.thinking) {
            updates.thinking = thinking;
        }
        if (finished !== undefined && message.loading !== !finished) {
            updates.loading = !finished;
        }
        if (
            thinkFinished !== undefined &&
            message.thinkLoading !== !thinkFinished
        ) {
            updates.thinkLoading = !thinkFinished;
        }
        if (books && books !== message.books) {
            updates.books = books
        }

        // 只有当有实际更新时才应用更改
        if (Object.keys(updates).length > 0) {
            Object.assign(message, updates);
        }
    }
};

const sendMessage = async () => {
    const userMessage = senderValue.value.trim();
    if (!userMessage) {
        ElMessage.warning("请先输入需要询问的药品或疾病");
        return;
    }
    if (isAiTyping.value) {
        ElMessage.warning("AI正在回复中，请稍候...");
        return;
    }
    // 清空输入框和附件
    senderValue.value = "";
    // 添加用户消息
    addMessage(Date.now() + Math.random(), userMessage, "user");
    isAiTyping.value = true;
    try {
        const aiMessage = addMessage(Date.now() + Math.random(), "", "ai");
        await KnowledgeService.sendMessageStream(
            {
                message: messageList.value,
            },
            (chunk) => {
                // 更新消息内容
                updateMessage(
                    aiMessage.id,
                    chunk.content,
                    chunk.thinking,
                    chunk.finished,
                    chunk.thinkFinished
                );
            },
            (chunk) => {
                // 更新消息内容
                updateMessage(aiMessage.id, "", "", false, false, chunk);
            }
        );

    } catch (error) {
        addErrorMessage("抱歉，我现在无法回复您的消息，请稍后再试。");
    } finally {
        isAiTyping.value = false;
    }
}

const stopChat = () => {
    KnowledgeService.abortCurrentRequest();
    isAiTyping.value = false;
    addErrorMessage("输出已中断");
};

</script>

<template>
    <div class="chat-input">
        <div class="chat-input-content">
            <MentionSender v-model="senderValue" @submit="sendMessage" variant="updown"
                :auto-size="{ minRows: 2, maxRows: 6 }" class="chat-textarea">
                <template #action-list style="display: flex">
                    <div class="chat-input-main-button">
                        <!-- AI正在回复时显示停止按钮 -->
                        <el-button @mousedown.stop v-if="isAiTyping" circle @click="stopChat()">
                            <el-icon class="stop-icon">
                                <StopIcon />
                            </el-icon>
                        </el-button>
                        <!-- AI未回复时显示发送按钮 -->
                        <el-button @mousedown.stop v-else circle @click="sendMessage()">
                            <el-icon class="submit-icon">
                                <SubmitArrowIcon />
                            </el-icon>
                        </el-button>
                    </div>
                </template>
            </MentionSender>
        </div>
        <div class="chat-input-remarks">内容由 AI 生成，请仔细甄别</div>
    </div>
</template>
<style scoped src="./ChatInput.css"></style>
