import NlsClient from './NlsClient.js';
import { http } from '@/extensions/http.js';
import { httpApis } from '@/hooks/useHttpApi.js';

function uuid() {
    return ([1e7] + -1e3 + -4e3 + -8e3 + -1e11).replace(/[018]/g, c =>
        (c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16)
    ).replace(/-/g, '');
}

class SpeechTranscription {
    constructor() {
        this._events = {};
    }

    defaultStartParams() {
        return {
            format: 'pcm',
            sample_rate: 16000,
            // customization_id: '74ae46e616b1417a851d17878d726947', // 自学习模型ID,
            disfluency: false, // 过滤语气词，即声音顺滑，默认值false（关闭）
            max_sentence_silence: 800, // 语音断句检测阈值，静音时长超过该阈值会被认为断句，参数范围200ms～6000ms，默认值800ms
            // speech_noise_threshold: 0.3, // 噪音参数阈值，参数范围：[-1,1]。取值说明如下：取值越趋于-1，噪音被判定为语音的概率越大。取值越趋于+1，语音被判定为噪音的概率越大。
            enable_intermediate_result: true, // 是否返回中间识别结果，默认是false
            enable_punctuation_prediction: true, // 是否在后处理中添加标点，默认是false。
            enable_inverse_text_normalization: true, // ITN（逆文本inverse text normalization）中文数字转换阿拉伯数字。设置为True时，中文数字将转为阿拉伯数字输出，默认值：False。
            enable_semantic_sentence_detection: false // 是否开启语义断句
        };
    }

    on(event, handler) {
        const handlers = this._events[event] || [];
        handlers.push(handler);
        this._events[event] = handlers;
    }

    emit(event, payload) {
        if (this._events[event]) {
            for (let i = 0; i < this._events[event].length; i++) {
                this._events[event][i](payload);
            }
        }
    }

    async getToken() {
        return http.get(httpApis.getNlsAccessToken, {
            local: true
        }).then(res => res.data);
    }

    async start(param) {
        this._config = await this.getToken();
        this._client = new NlsClient(this._config);
        this._taskId = uuid();
        let req = {
            header: {
                message_id: uuid(),
                task_id: this._taskId,
                namespace: 'SpeechTranscriber',
                name: 'StartTranscription',
                appkey: this._config.appKey
            },
            payload: param
        };
        // eslint-disable-next-line no-async-promise-executor
        return new Promise(async (resolve, reject) => {
            try {
                await this._client.start(
                    // onmessage
                    (msgObj) => {
                        const str = JSON.stringify(msgObj);
                        if (msgObj.header.name === 'TranscriptionStarted') {
                            this.emit('started', str);
                            resolve(str);
                        }
                        else if (msgObj.header.name === 'TranscriptionResultChanged') {
                            this.emit('changed', str);
                        }
                        else if (msgObj.header.name === 'TranscriptionCompleted') {
                            this.emit('TranscriptionCompleted', str);
                        }
                        else if (msgObj.header.name === 'SentenceBegin') {
                            this.emit('begin', str);
                        }
                        else if (msgObj.header.name === 'SentenceEnd') {
                            this.emit('end', str);
                        }
                        else if (msgObj.header.name === 'TaskFailed') {
                            this._client.shutdown();
                            this._client = null;
                            this.emit('TaskFailed', str);
                            this.emit('failed', str);
                        }
                    },
                    // onclose
                    () => {
                        this.emit('closed');
                    });
                this._client.sendMessage(JSON.stringify(req), false);
            }
            catch (error) {
                reject(error);
            }
        });
    }

    async close(param) {
        if (this._client == null) {
            return new Promise((resolve, reject) => {
                setTimeout(() => {
                    // reject('client is null');
                    resolve();
                }, 0);
            });
        }

        let req = {
            header: {
                message_id: uuid(),
                task_id: this._taskId,
                namespace: 'SpeechTranscriber',
                name: 'StopTranscription',
                appkey: this._config.appKey
            },
            payload: param,
            context: this._client.defaultContext()
        };

        return new Promise((resolve, reject) => {
            this.on('TranscriptionCompleted', (msg) => {
                if (this._client) {
                    this._client.shutdown();
                    this._client = null;
                }
                this.emit('completed', msg);
                resolve(msg);
            });

            this.on('TaskFailed', (msg) => {
                reject(msg);
            });

            this._client.sendMessage(JSON.stringify(req), false);
        });
    }

    sendAudio(data) {
        if (this._client == null) {
            return false;
        }

        this._client.sendBinaryMessage(data);
        return true;
    }
}
export default SpeechTranscription;
