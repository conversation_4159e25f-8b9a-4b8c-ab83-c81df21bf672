@font-face {
  font-family: "iconfont"; /* Project id 4895206 */
  src: url('iconfont.woff2?t=1749780823724') format('woff2'),
       url('iconfont.woff?t=1749780823724') format('woff'),
       url('iconfont.ttf?t=1749780823724') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-zhenduan:before {
  content: "\e7b9";
}

.icon-zhenduan1:before {
  content: "\e937";
}

.icon-ai-zhenduan:before {
  content: "\e660";
}

.icon-shengwen:before {
  content: "\e60b";
}

.icon-yuyin1:before {
  content: "\e60c";
}

.icon-shengwen2:before {
  content: "\ea7f";
}

.icon-database:before {
  content: "\ef91";
}

.icon-duomotaiku:before {
  content: "\e7ad";
}

.icon-duomotai:before {
  content: "\e9af";
}

.icon-send:before {
  content: "\e611";
}

.icon-luyin-cancel:before {
  content: "\e613";
}

.icon-luyin-pause:before {
  content: "\e601";
}

.icon-luyin-stop:before {
  content: "\e9a2";
}

.icon-turn-off:before {
  content: "\f313";
}

.icon-sidebar-right:before {
  content: "\e60f";
}

.icon-sidebar-left:before {
  content: "\e610";
}

.icon-female:before {
  content: "\e71a";
}

.icon-male:before {
  content: "\e71c";
}

.icon-arrow:before {
  content: "\e645";
}

.icon-mobile:before {
  content: "\e624";
}

.icon-upload:before {
  content: "\e96a";
}

.icon-history:before {
  content: "\e755";
}

.icon-paizhao:before {
  content: "\e672";
}

.icon-upload1:before {
  content: "\e6df";
}

.icon-cat:before {
  content: "\e748";
}

.icon-download:before {
  content: "\e65e";
}

.icon-dog:before {
  content: "\e82d";
}

.icon-list:before {
  content: "\e67d";
}

.icon-back:before {
  content: "\e60d";
}

.icon-sync:before {
  content: "\e614";
}

.icon-swap:before {
  content: "\e615";
}

.icon-arrow-down:before {
  content: "\e700";
}

.icon-stop:before {
  content: "\e885";
}

.icon-circle-stop:before {
  content: "\e600";
}

.icon-luyin:before {
  content: "\e63e";
}

.icon-luyin-outline:before {
  content: "\e651";
}

.icon-cai:before {
  content: "\e99d";
}

.icon-stop-line:before {
  content: "\edc4";
}

.icon-copy:before {
  content: "\e608";
}

.icon-new-chat:before {
  content: "\eaf3";
}

.icon-zan:before {
  content: "\e99e";
}

.icon-up:before {
  content: "\eaf4";
}

.icon-forward:before {
  content: "\eaf5";
}

.icon-down:before {
  content: "\eaf6";
}

.icon-arrow-up:before {
  content: "\eaf7";
}

.icon-assistant:before {
  content: "\e6c5";
}

