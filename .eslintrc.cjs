module.exports = {
    root: true,
    parser: 'vue-eslint-parser',
    parserOptions: {
        ecmaVersion: 2020,
        sourceType: 'module',
        ecmaFeatures: {
            jsx: true
        }
    },
    env: {
        browser: true,
        es6: true
    },
    globals: {
        process: 'readonly'
    },
    extends: [
        'eslint:recommended',
        'plugin:vue/recommended'
    ],
    // check if imports actually resolve
    settings: {
        'import/resolver': {
            alias: {
                map: [
                    ['@', './src'],
                ],
                extensions: ['.ts', '.js', '.jsx', '.json', '.vue'],
            },
        },
    },
    // add your custom rules here
    rules: {
        'no-console': 'off',
        // allow async-await
        'generator-star-spacing': 'off',
        // allow debugger during development
        'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'off',
        indent: [
            'error',
            4
        ],
        'linebreak-style': [
            'error',
            'unix'
        ],
        quotes: [
            'error',
            'single'
        ],
        semi: [
            'error',
            'always'
        ],
        'no-new': ['off'],
        // camelcase: ['warn', {
        //     properties: 'never'
        // }],
        camelcase: 'off',
        'brace-style': ['error', 'stroustrup'],
        'space-before-blocks': ['error', 'always'],
        'space-infix-ops': ['error'],
        'space-unary-ops': ['error', {
            words: true,
            nonwords: false
        }],
        'keyword-spacing': ['error', {
            before: true,
            after: true
        }],
        'arrow-spacing': ['error', {
            before: true,
            after: true
        }],
        'space-before-function-paren': ['error', {
            anonymous: 'always',
            named: 'never',
            asyncArrow: 'always'
        }],
        'semi-spacing': ['error', {
            before: false,
            after: true
        }],
        'comma-spacing': ['error', {
            before: false,
            after: true
        }],
        'key-spacing': ['error', {
            afterColon: true // 对象冒号后面必须留空格
        }],
        // 'operator-assignment': ['error', 'always'],
        'spaced-comment': ['warn', 'always', {
            line: {
                markers: ['/'],
                exceptions: ['-', '+']
            },
            block: {
                markers: ['!', '*!'],
                exceptions: ['*'],
                balanced: true
            }
        }],
        'no-unused-vars': ['error', {
            args: 'none',
            varsIgnorePattern: '[_e]'
        }],
        'newline-per-chained-call': ['error', {
            ignoreChainWithDepth: 4
        }],
        'object-curly-spacing': ['error', 'always'],
        'object-curly-newline': ['error', {
            'ObjectExpression': {
                'multiline': true,
                'minProperties': 1
            },
            'ObjectPattern': {
                'multiline': true,
                'minProperties': 1
            },
            'ImportDeclaration': 'never',
            'ExportDeclaration': {
                'multiline': true,
                'minProperties': 3
            }
        }],
        'template-curly-spacing': ['error', 'never'],
        'vue/html-indent': ['error', 4],
        'vue/html-self-closing': [
            'error',
            {
                html: {
                    void: 'never',
                    normal: 'never',
                    component: 'always'
                },
                svg: 'always',
                math: 'always'
            }
        ],
        'vue/max-attributes-per-line': 'off',
        'vue/attributes-order': 'off',
        'vue/no-unused-components': 'off',
        'vue/no-side-effects-in-computed-properties': 'off',
        'vue/singleline-html-element-content-newline': 'off',
        'vue/multi-word-component-names': 'off'
    }
};
