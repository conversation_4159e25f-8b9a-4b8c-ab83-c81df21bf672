.collapse-layout {
    height: 90dvh;
    overflow: auto;
    width: 100%;
    outline: 2px solid rgb(186, 186, 186);
    border-radius: 1dvw;
}

.collapse-container {
    padding: 0.5dvw 1dvw;
    width: 93%;
}

.collapse-base-info{
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5dvw 1dvw;
}

.collapse-body-info{
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: 0.5dvw 1dvw;
}

.pet-age-input-number{
    width: 70px;
}

.pet-body-input-number{
    width: 110px;
}

.pet-age-title{
    padding-inline: 8px
}