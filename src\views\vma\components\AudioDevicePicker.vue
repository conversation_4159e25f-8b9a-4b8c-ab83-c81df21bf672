<template>
    <div>
        <el-select
            v-model="selectedDeviceId"
            @change="handleDeviceChange"
            placeholder="选择麦克风" style="width: 100%; margin-bottom: 12px"
        >
            <el-option
                v-for="device in audioDevices"
                :key="device.deviceId"
                :label="device.label || '麦克风设备'"
                :value="device.deviceId"
            />
        </el-select>
    </div>
</template>
<script setup>
import { ref, onMounted, watch, nextTick } from 'vue';
import { useToast } from '@/hooks/useToast.js';
import { useLocalStorage } from '@vueuse/core';
const props = defineProps({
    modelValue: {
        type: String,
        default: '',
    },
    disabled: {
        type: Boolean,
        default: false,
    },
});
const emit = defineEmits(['update:modelValue']);

const audioDevices = ref([]);
const selectedDeviceId = useLocalStorage('voice_device_id', null);

const getAudioDevices = async () => {
    const permissionStatus = await navigator.permissions.query({
        name: 'microphone'
    });
    if (permissionStatus.state === 'granted') {
        console.log('用户已授权麦克风');
    }
    else {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: true
            });
            console.log('用户已授权麦克风');
            // 记得停止使用麦克风
            stream.getTracks().forEach(track => track.stop());
        }
        catch (err) {
            if (err.name === 'NotAllowedError') {
                useToast({
                    message: '请允许使用麦克风',
                    type: 'error'
                });
            }
            else if (err.name === 'NotFoundError') {
                useToast({
                    message: '未找到麦克风设备',
                    type: 'error'
                });
            }
            else {
                console.log('其他错误:', err);
            }
        }
    }
    const devices = await navigator.mediaDevices.enumerateDevices();
    audioDevices.value = devices.filter(device => device.kind === 'audioinput');
    if (audioDevices.value.length > 0 && !props.modelValue) {
        const usedDevice = devices.find(device => device.deviceId === selectedDeviceId.value);
        if (!usedDevice) {
            selectedDeviceId.value = audioDevices.value[0].deviceId;
        }
        await nextTick(() => {
            handleDeviceChange();
        });
    }
};
const handleDeviceChange = () => {
    emit('update:modelValue', selectedDeviceId.value);
};
onMounted(() => {
    getAudioDevices();
});
watch(() => props.modelValue, (newVal) => {
    selectedDeviceId.value = newVal;
});
</script>


<style scoped lang="scss">

</style>
