<template>
    <span ref="markRef" class="llm-mark" :class="['llm-' + props.type]" @click="handleClick">
        <slot></slot>
    </span>
</template>

<script setup>
import { defineProps, ref, useSlots } from 'vue';
import { useShowDrawer } from '@/ui/index';
import ChatBox from '@/views/chat/widgets/ChatBox.vue';
const markRef = ref(null);
const props = defineProps({
    type: {
        type: String,
        default: ''
    }
});
const handleClick = () => {
    if (!props.type) {
        return;
    }
    const text = markRef.value.innerText;
    useShowDrawer(ChatBox, {
        query: text,
        scene: 'knowledge',
        type: props.type,
        title: '我是知识查询助手，需要我帮你查什么呢？',
        subtitle: '关于疾病、药品相关的知识都可以问我哦~',
        settings: {
            rows: 1,
            tools: {
                // 化验图片
                laboratory: false,
                // 影像图片
                image: false,
            }
        }
    }, {
        title: '知识查询',
        width: '600px',
        bodyClass: 'rp-dialog-body-chatbox'
    });
};
</script>

<style scoped lang="scss">
.llm-mark {
    display: inline-block;
    color: var(--el-color-primary);
    text-decoration: 1px dashed underline;
    //border-bottom: 1px dashed #1D78EB;
    cursor: pointer;
}
</style>
<style lang="scss">
.rp-dialog-body-chatbox {
    padding: 0;
    .chat-container-inner {
        padding-top: 0;
    }
    .chat-container {
        padding: 0 20px;
    }
}
</style>
