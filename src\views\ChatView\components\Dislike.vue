<script setup lang="ts">
import { DISLIKE_CATEGORIES } from "@/constants/constant";
import { useChatStore } from "@/stores/chat";
import { ElMessage } from "element-plus";
import { computed, ref, watch } from "vue";

interface Props {
  visible: boolean;
  messageId: number | null;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  "update:visible": [value: boolean];
  close: [];
}>();

const chatStore = useChatStore();

// 不喜欢反馈对话框相关状态
const selectedDislikeType = ref<number>();
const dislikeDescription = ref("");
const helpfulAdvice = ref("");
const buttonDisabled = computed(() => !selectedDislikeType.value && !dislikeDescription.value && !helpfulAdvice.value);

// 使用常量中的不喜欢类别选项
const dislikeCategories = DISLIKE_CATEGORIES;

// 监听对话框显示状态，重置表单
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      selectedDislikeType.value = undefined;
      dislikeDescription.value = "";
      helpfulAdvice.value = "";
    }
  }
);

// 选择不喜欢类别
const selectDislikeType = (type: number) => {
  if (selectedDislikeType.value === type) {
    selectedDislikeType.value = undefined
  } else {
    selectedDislikeType.value = type;
  }
};

// 提交不喜欢反馈
const submitDislikeFeedback = async () => {
  if (!props.messageId) {
    ElMessage.warning("消息选择有误");
    return;
  }

  try {
    await chatStore.dislikeMessage(
      props.messageId,
      selectedDislikeType.value,
      dislikeDescription.value,
      helpfulAdvice.value
    );
    ElMessage.success("感谢您的反馈！");
    emit("update:visible", false);
    emit("close");
  } catch (error) {
    ElMessage.error("提交反馈失败，请稍后重试");
  }
};

// 取消不喜欢反馈对话框
const cancelDislikeFeedback = () => {
  emit("update:visible", false);
  emit("close");
  selectedDislikeType.value = undefined;
  dislikeDescription.value = "";
  helpfulAdvice.value = "";
};
</script>

<template>
  <!-- 不喜欢反馈对话框 -->
  <el-dialog :model-value="visible" @update:model-value="$emit('update:visible', $event)" width="700px"
    :before-close="cancelDislikeFeedback" class="dislike-feedback-dialog" top="8dvh">
    <div class="dislike-feedback-container">
      <div class="dislike-feedback-category">
        <div class="dislike-feedback-title">很抱歉，请告诉哪里有问题，我们会继续改进</div>
        <div class="dislike-feedback-category-list">
          <div v-for="category in dislikeCategories" :key="category.type" class="dislike-feedback-category-item"
            :class="{ active: selectedDislikeType === category.type }" @click="selectDislikeType(category.type)">
            <div class="dislike-feedback-category-text">{{ category.label }}</div>
          </div>
        </div>
      </div>

      <div class="dislike-feedback-desc">
        <div class="dislike-feedback-title">其他问题</div>
        <div class="dislike-feedback-helpful-input">
          <el-input class="dislike-feedback-helpful-input-textarea" v-model="dislikeDescription" type="textarea"
            :autosize="{ minRows: 4, maxRows: 6 }" placeholder="请描述遇到的问题..." />
        </div>
      </div>

      <div class="dislike-feedback-helpful">
        <div class="dislike-feedback-title">是否愿意帮助改进答案?</div>
        <div class="dislike-feedback-helpful-input">
          <el-input class="dislike-feedback-helpful-input-textarea" v-model="helpfulAdvice" type="textarea"
            :autosize="{ minRows: 4, maxRows: 6 }" placeholder="请提供正确答案或您期望的结果..." />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button class="dialog-footer-button-cancel" @click="cancelDislikeFeedback">取消</el-button>
        <el-button class="dialog-footer-button-submit" type="primary" @click="submitDislikeFeedback"
          :disabled="buttonDisabled">
          提交反馈
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped src="./Dislike.css"></style>
