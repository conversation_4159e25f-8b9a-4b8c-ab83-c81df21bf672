<template>
    <div class="mobile-chat-header">
        <div class="btn" @click="openSidebar">
            <rp-icon name="icon-history" size="22" />
        </div>
        <div class="title">
            <GlobalNavigatorMobile>好兽医AI助手</GlobalNavigatorMobile>
        </div>
        <div class="btn" @click="openNewChat">
            <rp-icon name="icon-new-chat" size="22" />
        </div>
    </div>
</template>

<script setup>
import { useRouter, useRoute } from 'vue-router';
import GlobalNavigatorMobile from '@/components/common/GlobalNavigatorMobile.vue';
const router = useRouter();
const route = useRoute();
const props = defineProps({
    isSidebarCollapsed: {
        type: Boolean,
        default: false
    }
});
const emit = defineEmits(['update:isSidebarCollapsed']);
const openSidebar = () => {
    emit('update:isSidebarCollapsed', !props.isSidebarCollapsed);
};
const openNewChat = () => {
    router.push({
        name: 'ChatNew',
        query: {
            scene: route.query.scene || '',
            t: Date.now()
        }
    });
};
</script>

<style scoped lang="scss">
.is-mobile {
    .mobile-chat-header {
        display: flex;
    }
}
.mobile-chat-header {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    background: #f6f6f6;
    z-index: 100;
    padding: 0 5px;
    display: none;
    align-items: center;
    justify-content: center;
    .title {
        flex: 1;
        text-align: center;
        font-weight: 500;
        font-size: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .btn {
        width: 48px;
        height: 48px;
        line-height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
</style>
