<template>
    <div class="laboratory-ocr">
        <div class="ocr">
            <div class="files">
                <el-upload
                    class="image-uploader"
                    drag
                    multiple
                    accept="image/*"
                    :http-request="handleUpload"
                    :show-file-list="false"
                    :auto-upload="false"
                    @change="onFileChange"
                >
                    <div>
                        <rp-icon name="upload" size="30" />
                        <div class="el-upload__text">将文件拖到此处，或 <em>点击上传</em></div>
                        <div class="el-upload__tip">支持截图粘贴、拖拽、多文件</div>
                    </div>
                </el-upload>
                <div class="image-list">
                    <!--
                    <div class="image-item" v-for="row in uploadList" :key="row.key">
                        <img :src="thumbnail(row.url, 60, 60)" alt="">
                    </div>
                    -->
                    <div class="image-item" v-for="(row) in fileList" :key="row.value.index">
                        <el-image
                            style="width: 70px;height: 70px;"
                            :src="row.value.url" fit="contain"
                            show-progress
                            :preview-src-list="filePreviewList"
                        />
                        <span class="status running" v-if="row.value.status===1">
                            <rp-icon name="loading" class="is-loading" />
                            提取中
                        </span>
                        <span class="status success" v-else-if="row.value.status===2">提取完成</span>
                        <span class="status error" v-else>提取失败</span>
                    </div>
                </div>
            </div>
            <div class="ocr-result">
                <div class="ocr-result-list">
                    <div class="report-result" v-for="(report, idx) in recognizeTaskResult" :key="idx">
                        <div v-if="report.pet">
                            {{ report.pet.name }} {{ report.pet.gender }} {{ report.pet.age }} {{ report.pet.species }} {{ report.pet.breed }} {{ report.pet.weight }}
                        </div>
                        <table class="report-item-table">
                            <caption style="font-size: 12px;color:#ecac0b;text-align: left;">以下为提取的化验结果，如有识别错误的，可以手动进行修正</caption>
                            <thead>
                                <tr>
                                    <th width="30%">指标</th>
                                    <th>值</th>
                                    <th class="center">单位</th>
                                    <th class="center">结果</th>
                                    <th class="center">参考值</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(row) in report.items" :key="row.name">
                                    <td>
                                        <editable-text v-model="row.name" />
                                    </td>
                                    <td>
                                        <editable-text v-model="row.value" />
                                    </td>
                                    <td class="center">
                                        <editable-text v-model="row.unit" />
                                    </td>
                                    <td class="center">
                                        <editable-text v-model="row.result" />
                                    </td>
                                    <td class="center">
                                        <editable-text v-model="row.referenceRange" />
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <div>备注：{{ report.remark }}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="el-dialog__footer">
            <el-button @click="$emit('close')">取消</el-button>
            <el-button type="primary" @click="sendMessage" :disabled="recognizeTaskResult.length===0">上传完成</el-button>
        </div>
    </div>
</template>

<script setup>
import { computed, onMounted, onUnmounted, reactive, ref, toRaw, unref } from 'vue';
import { file2base64 } from '@/utils/index';
import { http } from '@/extensions/http';
import { httpApis } from '@/hooks/useHttpApi';
import EditableText from '@/ui/form/EditableText.vue';
import RpIcon from '@/ui/icon/icon.vue';
import { uploadCosFile } from '@/hooks/useCos.js';
const props = defineProps({
    resultType: {
        type: String,
        default: 'markdown' // markdown | json
    },
    uploadType: {
        type: String,
        default: 'base64' // base64 | url
    }
});
const emit = defineEmits(['ok']);

// const uploadList = ref([]);
const fileList = ref([]);
let taskIndex = 1;
// const reportItems = reactive([]);
const recognizeTaskResult = reactive([]);
// uploadList.value.push({
//     key: 1,
//     url: 'http://rppet-fe-1312829983.cos.ap-shanghai.myqcloud.com/ai/vet/dev/files/2025/04/21/4f9935bb11805d5a20f7e472e3ab7a6c.png'
// }, {
//     key: 2,
//     url: 'http://rppet-fe-1312829983.cos.ap-shanghai.myqcloud.com/ai/vet/dev/files/2025/04/21/1250c63f7fb535236b5e3a5a123f9439.png'
// });
const filePreviewList = computed(() => {
    return fileList.value.map(item => {
        return item.value.url;
    });
});
const sendMessage = () => {
    if (recognizeTaskResult.length === 0) {
        return;
    }
    if (props.resultType === 'markdown') {
        const result = recognizeTaskResult.map(item => {
            const md = `| 检查项目 | 结果值 | 单位 | 结果判读 | 参考范围 |
|:--------|:--------:|:--------:|--------|--------|
${item.items.map(it => {
        return `| ${it.name} | ${it.value} | ${it.unit} | ${it.result} | ${it.referenceRange} |`;
    }).join('\n')}
    `;
            return {
                pet: toRaw(item.pet),
                text: md,
                remark: item.remark,
                image: item.content.result
            };
        });
        emit('ok', result);
    }
    else {
        const result = recognizeTaskResult.map(item => {
            return {
                pet: toRaw(item.pet),
                items: toRaw(item.items),
                remark: item.remark,
                image: item.url
            };
        });
        emit('ok', result);
    }
};
// 处理上传
const handleUpload = async ({
    file
}) => {
    try {
        await createRecognizeTask(file);
    }
    catch (err) {
        console.log(err);
        // ElMessage.error('上传失败');
    }
};

const createRecognizeTask = async (file) => {
    const task = ref({
        index: taskIndex++,
        status: 1, // 1：运行中 2：成功 3：失败
        // content: res,
        items: [],
        pet: null,
        remark: ''
    });
    if (props.uploadType === 'base64') {
        const res = await file2base64(file);
        task.value.content = res;
        task.value.url = res.result;
    }
    else {
        const res = await uploadCosFile(file, {}, 'lab-report');
        task.value.url = res.url;
    }
    fileList.value.push(task);
    recognize(task);
};
/**
 * 图片指标识别
 * @param task
 */
const recognize = (task) => {
    let params = {};
    if (props.uploadType === 'base64') {
        params = {
            base64: task.value.content.base64,
            mineType: task.value.content.mineType
        };
    }
    else {
        params = {
            url: task.value.url
        };
    }
    http.post(httpApis.recognizeLaboratoryReport, params, {
        timeout: 0
    }).then(res => {
        console.log(res.data);
        const report = res.data;
        if (Array.isArray(report.items) || report.remark) {
            task.value.status = 2;
            task.value.items = report.items;
            task.value.pet = report.pet;
            task.value.remark = report.remark;
            recognizeTaskResult.push(unref(task));
            // items.forEach(item => {
            //     const findIndex = reportItems.findIndex(item2 => item2.name === item.name);
            //     if (findIndex !== -1) {
            //         reportItems.splice(findIndex, 1, item);
            //     }
            //     else {
            //         reportItems.push(item);
            //     }
            // });
        }
        else {
            task.value.status = 3;
        }
    }).catch(e => {
        console.log(e);
        task.value.status = 3;
    });
};

// 粘贴上传
const handlePaste = async (event) => {
    const items = event.clipboardData?.items;
    if (!items) return;

    for (const item of items) {
        if (item.type.startsWith('image/')) {
            const file = item.getAsFile();
            if (file) {
                await handleUpload({
                    file
                });
            }
        }
    }
};

// 拦截 input 文件变化
const onFileChange = (upload) => {
    const files = upload?.target?.files || upload.raw;
    if (!files) return;

    const list = Array.isArray(files) ? files : [files];
    list.forEach(async file => {
        await handleUpload({
            file
        });
    });
};

onMounted(() => {
    window.addEventListener('paste', handlePaste);
});
onUnmounted(() => {
    window.removeEventListener('paste', handlePaste);
});
</script>

<style scoped lang="scss">
.ocr {
    max-height: 600px;
    overflow-y: auto;
    display: flex;
    flex-wrap: wrap;
}
.files {
    width: 250px;
}
.ocr-result {
    flex: 1;
    margin-left: 10px;
    height: 100%;
    overflow-y: auto;
    min-width: 300px;
}
.image-uploader {
    border-radius: 6px;
    text-align: center;
    cursor: pointer;
}
.input-box {
    margin-bottom: 10px;
}
.image-list {
    margin-top: 10px;
    display: grid;
    gap: 10px;
    grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));
    .image-item {
        border: 1px solid #ddd;
        border-radius: 5px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        img {
            max-width: 70px;
            height: 70px;
        }
        .status {
            margin-top: 4px;
            font-size: 12px;
            &.running {}
            &.success {
                color: #67c23a;
            }
            &.error {
                color: #f56c6c;
            }
        }
    }
}
.report-item-table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
    margin-bottom: 10px;
    position: relative;
    text-align: left;
    thead {
        position: sticky;
        top: 0;
        th {
            padding: 4px;
        }
    }
    th, td {
        padding: 0;
        &.center {
            text-align: center;
        }
    }
    th {
        background-color: #f2f2f2;
    }
    td {
        border-bottom: 1px solid #ddd;
    }
}
</style>
