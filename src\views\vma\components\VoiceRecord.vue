<template>
    <div class="voice-recorder" v-loading="isDataProcessing" element-loading-text="数据处理中">
        <div class="content">
            <div class="btn">
                <el-button v-if="isRecording" type="danger" @click="() => stopRecording(false)" circle size="large">
                    <rp-icon name="icon-luyin-stop" size="40" />
                </el-button>
                <el-button v-else type="primary" @click="startRecording" circle size="large">
                    <rp-icon name="icon-luyin" size="40" />
                </el-button>
            </div>
            <div class="wave">
                <canvas ref="canvasRef" class="wave-canvas"></canvas>
            </div>
        </div>
        <div class="footer">
            <div class="control">
                <el-popover placement="top-start" :width="360" trigger="click">
                    <template #reference>
                        <rp-icon name="Tools" size="20" color="#555" style="cursor: pointer; vertical-align: middle;" />
                    </template>
                    选择麦克风：
                    <audio-device-picker v-model="audioDeviceId" />
                </el-popover>
            </div>
            <div class="text">
                <div v-if="voiceTask && voiceTask.status === TASK_STATUS_ERROR">
                    <span class="tips">录音数据处理失败，请重试！</span>
                    <el-button type="primary" icon="RefreshRight" size="small" round @click="executeVoiceTask">点击重试</el-button>
                </div>
                <div v-else>
                    <voice-text v-if="voiceText.length > 0" :text="voiceText.join('')" height="3em" />
                    <span v-else class="tips">开始录音前请确认已获得宠主同意</span>
                </div>
            </div>
            <div style="width: 20px;"></div>
        </div>
        <div v-if="env.DEV">
            {{ voiceText.join('') }}
        </div>
    </div>
</template>

<script setup>
import SpeechTranscription from '@/extensions/lib/SpeechTranscription';
import AudioDevicePicker from './AudioDevicePicker.vue';
import VoiceText from './VoiceText.vue';
import { onMounted, ref, unref, onUnmounted } from 'vue';
import { useToast } from '@/hooks/useToast';
import RpIcon from '@/ui/icon/icon.vue';
import { useVoiceMedicalRecordTask, TASK_STATUS_FINISH, TASK_STATUS_ERROR } from './VoiceMedicalRecordTask.js';

const canvasRef = ref(null);
const audioDeviceId = ref(null);

const env = import.meta.env;

const voiceTask = ref(null);
const isRecording = ref(false);
const isDataProcessing = ref(false);
// 录音任务是否已取消
const isCanceled = ref(false);

const MIN_RECORD_DURATION = 5 * 1000; // 最少录制时长(ms)
const MIN_TEXT_LENGTH = 10; // 最少文字长度
let startRecordTime = null;
let canvasCtx = null;
let animationFrameId = null;

const emit = defineEmits(['start', 'end']);
let audioContext;
let audioInput;
let audioStream;
let workletNode;
let analyser;
const voiceText = ref([]);
const st = new SpeechTranscription();
st.on('started', (msg) => {
    console.log('Client recv started');
    emit('start');
});

st.on('changed', (msg) => {
    // console.log('Client recv changed:', msg);
    const payload = JSON.parse(msg).payload;
    voiceText.value.splice(payload.index - 1, 1, payload.result);
});

st.on('completed', (msg) => {
    console.log('Client recv completed:', msg);
});

st.on('begin', (msg) => {
    // console.log('Client recv sentenceBegin:', msg);
});

st.on('end', (msg) => {
    // console.log('Client recv sentenceEnd:', msg);
    const payload = JSON.parse(msg).payload;
    voiceText.value.splice(payload.index - 1, 1, payload.result);
});

st.on('closed', async () => {
    console.log('Client recv closed');
    if (isCanceled.value) {
        return;
    }
    // 尝试停止录音，释放资源
    stopRecording();
    const text = unref(voiceText).join('');
    if (Date.now() - startRecordTime < MIN_RECORD_DURATION || text.length < MIN_TEXT_LENGTH) {
        voiceText.value = [];
        useToast({
            message: '录制声音太短了，请重新录制语音',
            type: 'warning'
        });
        return;
    }
    try {
        isDataProcessing.value = true;
        voiceTask.value = useVoiceMedicalRecordTask(audioChunks, text);
        const [result, status] = await voiceTask.value.start();
        if (status === TASK_STATUS_FINISH) {
            emit('end', result);
            audioChunks.length = 0;
            voiceText.value = [];
            voiceTask.value = null;
        }
        isDataProcessing.value = false;
    }
    catch (e) {
        console.log(e);
        isDataProcessing.value = false;
    }
});

st.on('failed', (msg) => {
    console.log('Client recv failed:', msg);
    // todo 识别异常处理
});
onMounted(() => {
});
onUnmounted(() => {
    stopRecording(); // 页面卸载自动释放资源
});
let waveformData = null; //  new Float32Array(512); // 存储最新的音频帧数据
const audioChunks = []; // 全局变量，收集音频块
/**
 * 语音病历创建失败后，重新执行创建任务
 * @return {Promise<void>}
 */
const executeVoiceTask = async () => {
    if (voiceTask.value) {
        isDataProcessing.value = true;
        const [result, status] = await voiceTask.value.retry();
        if (status === TASK_STATUS_FINISH) {
            emit('end', result);
            audioChunks.length = 0;
            voiceText.value = [];
            voiceTask.value = null;
        }
        isDataProcessing.value = false;
    }
};

const drawWaveform = () => {
    if (!canvasCtx) return;

    analyser.getByteTimeDomainData(waveformData); // 获取时域数据

    const width = canvasRef.value.width;
    const height = canvasRef.value.height;
    canvasCtx.clearRect(0, 0, width, height);

    // 渐变背景（视觉柔和）
    canvasCtx.fillStyle = 'rgba(255, 255, 255, 0)';
    canvasCtx.fillRect(0, 0, width, height);

    // 创建波形线条渐变
    const gradient = canvasCtx.createLinearGradient(0, 0, width, 0);
    gradient.addColorStop(0, 'rgba(236,127,22,0)');
    gradient.addColorStop(0.5, '#ec7f16');
    gradient.addColorStop(1, 'rgba(236,127,22,0)');

    canvasCtx.lineWidth = 2;
    canvasCtx.strokeStyle = gradient;
    canvasCtx.beginPath();

    const sliceWidth = width / waveformData.length;
    let x = 0;
    // 整体振幅提升因子
    const amplitudeBoost = 1;

    canvasCtx.beginPath();

    for (let i = 0; i < waveformData.length; i++) {
        const magnitude = waveformData[i];
        const y = height - (magnitude / 255) * height; // 反向绘制顶部为 255

        if (i === 0) {
            canvasCtx.moveTo(x, y);
        }
        else {
            canvasCtx.lineTo(x, y);
        }

        x += sliceWidth;
    }

    canvasCtx.strokeStyle = gradient;
    canvasCtx.lineWidth = 2;
    canvasCtx.stroke();

    animationFrameId = requestAnimationFrame(drawWaveform);
};
const drawWaveform1 = () => {
    if (!canvasCtx) return;
    analyser.getByteFrequencyData(waveformData);
    const width = canvasRef.value.width;
    const height = canvasRef.value.height;

    canvasCtx.clearRect(0, 0, width, height);

    // 尾迹效果
    canvasCtx.fillStyle = 'rgba(0, 0, 0, 0)';
    canvasCtx.fillRect(0, 0, width, height);

    // 渐变色
    const gradient = canvasCtx.createLinearGradient(0, 0, 0, height);
    gradient.addColorStop(0, '#acdcff');
    gradient.addColorStop(1, '#43b0ff');

    const centerX = width / 2;
    const barCount = waveformData.length;
    const halfCount = Math.floor(barCount / 2);
    const sliceWidth = width / barCount;

    for (let i = 0; i < halfCount; i++) {
        const value = waveformData[i];
        const percent = value / 255;
        const barHeight = percent * height;

        const barWidth = sliceWidth;

        // 左侧从中心向左绘制
        const xLeft = centerX - (i + 1) * barWidth;
        // const dWidth = xLeft / halfCount * barWidth;
        const dWidth = barWidth;
        const dHeight = xLeft / halfCount * barHeight;
        const y = height - dHeight;
        canvasCtx.fillStyle = gradient;
        canvasCtx.fillRect(xLeft, y, dWidth, dHeight);

        // 右侧从中心向右绘制
        const xRight = centerX + i * barWidth;
        canvasCtx.fillStyle = gradient;
        canvasCtx.fillRect(xRight, y, dWidth, dHeight);
    }

    animationFrameId = requestAnimationFrame(drawWaveform1);
};

const startRecording = async () => {
    if (isRecording.value) {
        return;
    }
    isCanceled.value = false;
    // 启动WebSocket
    await st.start(st.defaultStartParams());

    isRecording.value = true;
    voiceText.value = [];
    audioChunks.splice(0);

    try {
        // 获取音频输入设备
        audioStream = await navigator.mediaDevices.getUserMedia({
            audio: {
                noiseSuppression: true,     // 启用降噪
                echoCancellation: true,     // 回声消除
                autoGainControl: true,       // 自动增益控制
                deviceId: audioDeviceId.value ? {
                    exact: audioDeviceId.value
                } : undefined
            }
        });
        audioContext = new (window.AudioContext || window.webkitAudioContext)();
        audioInput = audioContext.createMediaStreamSource(audioStream);

        // 加载 worklet js 文件
        // await audioContext.audioWorklet.addModule('/src/views/vma/components/recorder-worklet.js');
        await audioContext.audioWorklet.addModule('/recorder-worklet.js');
        workletNode = new AudioWorkletNode(audioContext, 'recorder-worklet', {
            processorOptions: {
                sampleRate: audioContext.sampleRate
            }
        });
        workletNode.port.onmessage = (event) => {
            const buffer = event.data;
            const int16Buffer = new Int16Array(buffer);
            audioChunks.push(int16Buffer); // 收集
            st.sendAudio(buffer);
        };

        analyser = audioContext.createAnalyser();
        analyser.fftSize = 512;
        waveformData = new Uint8Array(analyser.frequencyBinCount);

        audioInput.connect(analyser);
        analyser.connect(workletNode);
        // audioInput.connect(workletNode);
        // workletNode.connect(audioContext.destination);
        canvasCtx = canvasRef.value.getContext('2d');
        drawWaveform();
        console.log('🎙️ AudioWorklet 已启动');
        startRecordTime = Date.now();
    }
    catch (e) {
        console.log('录音失败: ' + e);
        isRecording.value = false;
    }
};

const stopRecording = (cancel = false) => {
    if (!isRecording.value) {
        return;
    }
    isCanceled.value = cancel;
    if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
        animationFrameId = null;
    }

    if (workletNode) {
        workletNode.disconnect();
        workletNode.port.onmessage = null;
        workletNode = null;
    }
    if (audioInput) {
        audioInput.disconnect();
        audioInput = null;
    }
    if (audioStream) {
        audioStream.getTracks().forEach(track => track.stop());
        audioStream = null;
    }
    if (audioContext) {
        audioContext.close();
        audioContext = null;
    }
    st.close();
    if (canvasCtx && canvasRef.value) {
        canvasCtx.clearRect(0, 0, canvasRef.value.width, canvasRef.value.height);
    }
    setTimeout(() => {
        isRecording.value = false;
    }, 1000);

    console.log('🛑 录音停止，波形图清空');
};

defineExpose({
    startRecording,
    stopRecording
});
</script>

<style scoped lang="scss">
.voice-recorder {
    //border: 1px solid #ddd;
    padding: 20px;
    border-radius: 10px;
    .content {
        text-align: center;
    }
    .btn {
        padding: 30px;
        .el-button {
            --el-button-size: 90px;
            font-size: 50px;
            outline: 0 solid #cfe5ff;
            transition: outline-width 0.2s ease-in-out;
            &--primary:hover {
                //transform: scale(1.2);
                outline-width: 10px;
            }
        }
    }
    .wave-canvas {
        width: 100%;
        height: 50px;
    }
    .footer {
        display: flex;
        background: #fff;
        box-shadow: 1px 1px 5px rgba(0, 0, 0, 0.1);
        padding: 5px 10px;
        border-radius: 5px;
        align-items: center;
        .text {
            flex: 1;
            padding: 0 20px;
            text-align: center;
            height: 40px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    }
    .tips {
        color: #ec7f16;
    }
}
</style>
