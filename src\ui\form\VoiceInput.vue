<template>
    <div
        class="voice-input" :class="{
            'is-recording': isRecording
        }"
    >
        <el-input
            ref="inputRef"
            v-model="currentValue"
            :type="props.type"
            v-bind="$attrs"
            :autosize="{
                minRows: 2,
                maxRows: 6
            }"
            resize="none"
            style="width: 100%;"
            @input="handleInputChange"
            @scroll="handleInputScroll"
            @blur="handleInputBlur"
        />
        <div ref="coverRef" class="cover" style="display: none;">
            {{ inputSelectionValue.prev }}<span class="new-value">{{ voiceText.join('') }}</span>{{ inputSelectionValue.next }}
        </div>
        <div class="btn">
            <button type="button" v-if="isRecording" @click.stop.prevent="() => stopRecording(false)" class="item">
                <el-tooltip effect="dark" content="停止录音" placement="bottom" :show-after="200">
                    <rp-icon color="var(--el-color-danger)" name="icon-luyin-stop" size="16" />
                </el-tooltip>
            </button>
            <button type="button" v-else @click.stop.prevent="startRecording" class="item" :disabled="isVoiceBusy">
                <el-tooltip effect="dark" content="语音输入" placement="bottom" :show-after="200">
                    <rp-icon name="icon-luyin" size="16" />
                </el-tooltip>
            </button>
        </div>
        <div class="wave">
            <canvas ref="canvasRef" class="wave-canvas"></canvas>
        </div>
    </div>
</template>

<script setup>
import { computed, onMounted, onUnmounted, ref, watch } from 'vue';
import RpIcon from '@/ui/icon/icon.vue';
import { useVoiceRecord } from '@/hooks/useVoiceRecord.js';
import { useLocalStorage } from '@vueuse/core';

const inputRef = ref(null);
const coverRef = ref(null);
const inputSelectionIndex = ref(0);
const canvasRef = ref(null);
const audioDeviceId = useLocalStorage('voice_device_id', null);

let canvasCtx = null;
let animationFrameId = null;
const props = defineProps({
    modelValue: {
        type: String,
        default: ''
    },
    type: {
        type: String,
        default: 'textarea'
    }
});

const currentValue = ref('');
const beforeValue = ref('');
const emit = defineEmits(['update:modelValue']);
const inputSelectionValue = computed(() => {
    const text = beforeValue.value || '';
    return {
        prev: text.substring(0, inputSelectionIndex.value),
        next: text.substring(inputSelectionIndex.value),
    };
});
watch(() => props.modelValue, () => {
    currentValue.value = props.modelValue;
    if (inputSelectionIndex.value === 0) {
        inputSelectionIndex.value = props.modelValue.length;
    }
});
let waveformData = null; //  new Float32Array(512); // 存储最新的音频帧数据
const {
    startRecording,
    stopRecording,
    voiceText,
    isRecording,
    isVoiceBusy
} = useVoiceRecord({
    audioDeviceId: audioDeviceId,
    drawWave(analyser) {
        canvasCtx = canvasRef.value.getContext('2d');
        waveformData = new Uint8Array(analyser.frequencyBinCount);
        drawWaveform(analyser);
    },
    beforeStartRecording() {
        beforeValue.value = currentValue.value;
    },
    onVoiceTextChanged(text) {
        currentValue.value = inputSelectionValue.value.prev + text + inputSelectionValue.value.next;
    },
    onVoiceEnd(text) {
        currentValue.value = inputSelectionValue.value.prev + text + inputSelectionValue.value.next;
    },
    onVoiceClosed(text) {
        if (animationFrameId) {
            cancelAnimationFrame(animationFrameId);
            animationFrameId = null;
        }
        if (canvasCtx && canvasRef.value) {
            canvasCtx.clearRect(0, 0, canvasRef.value.width, canvasRef.value.height);
        }
        currentValue.value = inputSelectionValue.value.prev + text + inputSelectionValue.value.next;
        emit('update:modelValue', currentValue.value);
        voiceText.value = [];
        inputSelectionIndex.value += text.length;
    }
});

onMounted(() => {
});
onUnmounted(() => {
    stopRecording(); // 页面卸载自动释放资源
});

const drawWaveform = (analyser) => {
    if (!canvasCtx) return;

    analyser.getByteTimeDomainData(waveformData); // 获取时域数据

    const width = canvasRef.value.width;
    const height = canvasRef.value.height;
    canvasCtx.clearRect(0, 0, width, height);

    // 渐变背景（视觉柔和）
    canvasCtx.fillStyle = 'rgba(255, 255, 255, 0)';
    canvasCtx.fillRect(0, 0, width, height);

    // 创建波形线条渐变
    const gradient = canvasCtx.createLinearGradient(0, 0, width, 0);
    gradient.addColorStop(0, 'rgba(236,127,22,0)');
    gradient.addColorStop(0.5, '#ec7f16');
    gradient.addColorStop(1, 'rgba(236,127,22,0)');

    canvasCtx.lineWidth = 2;
    canvasCtx.strokeStyle = gradient;
    canvasCtx.beginPath();

    const sliceWidth = width / waveformData.length;
    let x = 0;

    canvasCtx.beginPath();

    for (let i = 0; i < waveformData.length; i++) {
        const magnitude = waveformData[i];
        const y = height - (magnitude / 255) * height; // 反向绘制顶部为 255

        if (i === 0) {
            canvasCtx.moveTo(x, y);
        }
        else {
            canvasCtx.lineTo(x, y);
        }

        x += sliceWidth;
    }

    canvasCtx.strokeStyle = gradient;
    canvasCtx.lineWidth = 2;
    canvasCtx.stroke();

    animationFrameId = requestAnimationFrame(() => drawWaveform(analyser));
};
const focus = () => {
    inputRef.value.focus();
};
const handleInputChange = (value) => {
    emit('update:modelValue', value);
};
const handleInputBlur = (e) => {
    inputSelectionIndex.value = e.target.selectionStart;
};
const handleInputScroll = e => {
    coverRef.value.scrollTop = e.target.scrollTop;
};
defineExpose({
    startRecording,
    stopRecording,
    focus
});
</script>

<style scoped lang="scss">
.voice-input {
    position: relative;
    width: 100%;
    .cover {
        pointer-events: none;
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        padding: 5px 11px;
        line-height: 1.5;
        color: transparent;
        white-space: pre-wrap;
        .new-value {
            color: #acacac;
        }
    }
    .btn {
        position: absolute;
        right: 0px;
        bottom: 0px;
        .item {
            cursor: pointer;
            padding: 8px;
            line-height: 1;
            border: none;
            background: none;
            appearance: none;
        }
    }
    .wave {
        pointer-events: none;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }
    .wave-canvas {
        width: 100%;
        height: 100%;
        //height: 50px;
        //background: #1D78EB;
    }
    :deep(textarea) {
        resize: none;
    }
    &.is-recording {
        .cover {
            pointer-events: auto;
        }
    }
}
</style>
