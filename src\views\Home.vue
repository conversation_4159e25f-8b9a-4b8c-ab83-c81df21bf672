<template>
    <div class="home">
        <el-container>
            <el-main>
                <div class="container">
                    <div class="hero-section">
                        <img class="logo" src="../assets/logo-white.png" alt="">
                        <h1 class="main-title animate__animated animate__fadeIn">好兽医AI助手</h1>
                        <!--
                        <div class="download-section animate__animated animate__fadeInUp">
                            <div class="download-options">
                                <div class="download-card" @click="handleDownload('android')">
                                    <div class="card-content">
                                        <img src="/android-logo.svg" alt="Android" class="platform-icon">
                                        <p>Android版</p>
                                    </div>
                                    <div class="card-overlay">
                                        <span>点击下载</span>
                                    </div>
                                </div>
                                <div class="download-card" @click="handleDownload('ios')">
                                    <div class="card-content">
                                        <img src="/apple-logo.svg" alt="iOS" class="platform-icon">
                                        <p>iOS版</p>
                                    </div>
                                    <div class="card-overlay">
                                        <span>点击下载</span>
                                    </div>
                                </div>
                            </div>
                            <div class="download-notice">
                                <el-alert
                                    title="下载暂未开放"
                                    type="info"
                                    :closable="false"
                                    effect="light"
                                    center
                                >
                                    <template #default>
                                        <div class="alert-content">
                                            <i class="el-icon-info-filled notice-icon"></i>
                                            <span>我们正在紧锣密鼓地准备中，敬请期待</span>
                                        </div>
                                    </template>
                                </el-alert>
                            </div>
                        </div>
                        -->
                        <div class="intro-section animate__animated">
                            <h2 class="subtitle">让宠物没有难治的病</h2>
                            <div class="feature">
                                <dl>
                                    <dt><rp-icon name="icon-database" />垂直深耕</dt>
                                    <dd>基于亿级独家病例库与权威专家验证的诊疗路径，专精打造宠物医疗垂直领域大模型</dd>
                                </dl>
                                <dl>
                                    <dt><rp-icon name="icon-duomotai" />多模态诊断</dt>
                                    <dd>融合文本/影像/病理等多模态数据，构建深度医疗决策体系，交叉验证降低误诊率</dd>
                                </dl>
                                <dl>
                                    <dt><rp-icon name="icon-duomotaiku" />全场景赋能</dt>
                                    <dd>集成语音病历/诊疗路径推演/RAG/知识图谱/多智能体协同技术，打通智能化诊疗全流程</dd>
                                </dl>
                            </div>
                            <el-button type="primary" size="large" class="try-button" @click="handleTryDemo">
                                <rp-icon name="icon-new-chat" style="margin-right: 5px;" />
                                立即体验
                            </el-button>
                            <!--
                            <el-button type="primary" size="large" class="try-button" @click="handleTryVoiceMedicalRecord">
                                <rp-icon name="Microphone" />
                                体验语音病历
                            </el-button>
                            -->
                        </div>
                    </div>
                </div>
            </el-main>
        </el-container>
        <div class="bottom-layer">
            <div class="about">
                <dl>
                    <dt>关于我们</dt>
                    <dd><strong>好兽医AI助手</strong>致力于为动物医疗行业提供智能化医疗解决方案，让宠物医疗更加便捷高效。</dd>
                </dl>
                <dl>
                    <dt>联系方式</dt>
                    <dd><EMAIL></dd>
                    <!--<dd>************</dd>-->
                </dl>
                <dl>
                    <dt>快速链接</dt>
                    <dd><a href="/privacy">隐私政策</a></dd>
                    <dd><a href="/agreement">服务条款</a></dd>
                </dl>
                <dl>
                    <dt>关注我们</dt>
                    <dd>
                        <el-popover placement="top" width="320">
                            <img src="../assets/qrcode_fwh.jpg" width="300" alt="">
                            <template #reference>
                                <div class="follow-item">
                                    <img src="../assets/qrcode_fwh.jpg" alt="">
                                    <div>服务号</div>
                                </div>
                            </template>
                        </el-popover>
                        <el-popover placement="top" width="320">
                            <img src="../assets/qrcode_sph.jpeg" width="300" alt="">
                            <template #reference>
                                <div class="follow-item">
                                    <img src="../assets/qrcode_sph.jpeg" alt="">
                                    <div>视频号</div>
                                </div>
                            </template>
                        </el-popover>
                    </dd>
                </dl>
            </div>
            <div class="copyright">
                <div>
                    <a class="text-color" href="https://beian.miit.gov.cn" target="_blank">粤ICP备2024314826号-6</a>
                </div>
                <div>&copy;2025 好兽医AI助手</div>
                <div>
                    广东省深圳市福田区沙头街道天安社区泰然七路1号博今商务广场A座二十二层2205-5
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ElMessage } from 'element-plus';
import 'animate.css';
import { useRouter } from 'vue-router';
import RpIcon from '@/ui/icon/icon.vue';
const router = useRouter();
const handleDownload = (platform) => {
    if (platform === 'android') {
    // 处理Android下载
        ElMessage({
            message: 'Android版本下载即将开放，敬请期待',
            type: 'info'
        });
    }
    else {
    // 处理iOS下载
        ElMessage({
            message: 'iOS版本下载即将开放，敬请期待',
            type: 'info'
        });
    }
};

const handleTryDemo = () => {
    router.push({
        name: 'ChatNew'
    });
};
const handleTryVoiceMedicalRecord = () => {
    router.push({
        name: 'VmaCreate'
    });
};
</script>

<style lang="scss" scoped>
// 移动端断点
$mobile-breakpoint: 768px;

.home {
  //background: linear-gradient(135deg, #f5f7fa 0%, #ffffff 100%);
    background: linear-gradient(to bottom, #ffffff, transparent), linear-gradient(to top, rgb(249 249 249), transparent), radial-gradient(circle at left 30%, transparent, #c7a1fc), radial-gradient(circle at right 30%, transparent, #26fff8);

    min-height: 100vh;

  .container {
    padding: 60px 20px;

    @media (max-width: $mobile-breakpoint) {
      padding: 40px 16px;
    }
  }

  .hero-section {
    text-align: center;
    max-width: 800px;
    margin: 0 auto 40px;
      .logo {
          width: 100px;
          border-radius: 15px;
      }
  }

  .main-title {
    font-size: 2.5rem;
    //background: linear-gradient(45deg, #0068b7, #4ab2c9);
    //-webkit-background-clip: text;
    color: #0068b7;
    margin-bottom: 10px;
    font-weight: 600;
    letter-spacing: -1px;
    //text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    @media (max-width: $mobile-breakpoint) {
      font-size: 2.2rem;
      margin-bottom: 10px;
    }
  }

  .download-section {
    margin: 60px 0;
    animation-delay: 0.2s;

    @media (max-width: $mobile-breakpoint) {
      margin: 40px 0;
    }

    .download-options {
      display: flex;
      justify-content: center;
      gap: 60px;
      margin-bottom: 20px;

      @media (max-width: $mobile-breakpoint) {
        flex-direction: column;
        align-items: center;
        gap: 20px;
      }
    }

    .download-notice {
      max-width: 400px;
      margin: 30px auto 0;

      @media (max-width: $mobile-breakpoint) {
        max-width: 100%;
        margin: 20px auto 0;
        padding: 0 16px;
      }

      :deep(.el-alert) {
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);

        .alert-content {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;

          .notice-icon {
            font-size: 16px;
            color: #909399;
          }
        }
      }
    }

    .download-card {
      width: 240px;
      height: 240px;
      background: #fff;
      border: 2px solid #eaeaea;
      border-radius: 20px;
      position: relative;
      overflow: hidden;
      cursor: pointer;
      transition: all 0.3s ease;

      @media (max-width: $mobile-breakpoint) {
        width: 200px;
        height: 200px;
      }

      &:hover {
        transform: translateY(-5px);
        border-color: var(--el-color-primary);
        box-shadow: 0 12px 28px rgba(64, 158, 255, 0.15);

        .card-overlay {
          opacity: 1;
        }

        .card-content {
          transform: translateY(-5px);
        }
      }

      // 在移动端，始终显示遮罩层
      @media (max-width: $mobile-breakpoint) {
        &:active {
          transform: scale(0.98);
        }

        .card-overlay {
          opacity: 1;
          background: linear-gradient(to top, rgba(64, 158, 255, 0.8), rgba(64, 158, 255, 0.6));
        }
      }

      .card-content {
        position: relative;
        z-index: 1;
        height: 100%;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        transition: transform 0.3s ease;

        .platform-icon {
          width: 100px;
          height: 100px;
          margin-bottom: 20px;
          filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));

          @media (max-width: $mobile-breakpoint) {
            width: 80px;
            height: 80px;
            margin-bottom: 16px;
          }
        }

        p {
          font-size: 1.4rem;
          color: #303133;
          font-weight: 500;

          @media (max-width: $mobile-breakpoint) {
            font-size: 1.2rem;
          }
        }
      }

      .card-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(to top, rgba(64, 158, 255, 0.9), rgba(64, 158, 255, 0.7));
        color: white;
        padding: 15px;
        opacity: 0;
        transition: all 0.3s ease;

        span {
          font-size: 1.1rem;
          font-weight: 500;

          @media (max-width: $mobile-breakpoint) {
            font-size: 1rem;
          }
        }
      }
    }
  }

  .intro-section {
    margin-top: 20px;
    text-align: center;
    padding: 0 20px;
    animation-delay: 0.4s;

    @media (max-width: $mobile-breakpoint) {
      margin-top: 10px;
      padding: 0 16px;
    }

    .subtitle {
      font-size: 1.6rem;
      color: #303133;
      font-weight: 500;
      position: relative;
      display: inline-block;
        margin-bottom: 60px;

      @media (max-width: $mobile-breakpoint) {
        font-size: 1.5rem;
        margin-bottom: 20px;
      }

      &::after {
        content: '';
        position: absolute;
        bottom: -8px;
        left: 50%;
        transform: translateX(-50%);
        width: 60px;
        height: 3px;
        background: linear-gradient(90deg, var(--el-color-primary), #36cfc9);
        border-radius: 3px;

        @media (max-width: $mobile-breakpoint) {
          width: 40px;
          height: 2px;
        }
      }
    }

    .description {
      font-size: 1.2rem;
      color: #606266;
      line-height: 1.8;
      margin-bottom: 40px;
      max-width: 720px;
      margin-left: auto;
      margin-right: auto;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);

      @media (max-width: $mobile-breakpoint) {
        font-size: 1.1rem;
        line-height: 1.6;
        margin-bottom: 30px;
      }
    }
      .feature {
          margin-bottom: 40px;
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
          gap: 10px;
          text-align: left;
          dl {
              flex: 1;
              background: #f0f6ff;
              padding: 10px;
              border-radius: 10px;
          }
          dt {
              font-size: 18px;
              font-weight: 500;
              margin-bottom: 10px;
          }
      }
  }
}
.bottom-layer {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    background: #fff;
    padding: 10px 0;
    font-size: 12px;
    color: #333;
    .about {
        max-width: 840px;
        padding: 0 20px;
        margin: 0 auto;
        display: grid;
        gap: 20px;
        grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
        dt {
            font-weight: 500;
            font-size: 14px;
            margin-bottom: 10px;
        }
        dd {
            color: #777;
            margin: 8px 0;
        }
        .follow-item {
            text-align: center;
            display: inline-block;
            + .follow-item {
                margin-left: 20px;
            }
            img {
                width: 80px;
                line-height: 1;
            }
        }
    }
    .copyright {
        text-align: center;
        color: #666;
        > div {
            display: inline-block;
            margin: 0 10px;
        }
    }
    a {
        color: inherit !important;
    }
}
.try-button {
    width: 180px;
}
// 添加动画延迟
.animate__fadeInUp {
  animation-duration: 0.8s;
}

// 移动端优化动画
@media (max-width: 600px) {
  .animate__fadeInUp {
    animation-duration: 0.5s;
  }
    .bottom-layer {
        position: static;
    }
}
@media (max-height: 730px) {
    .bottom-layer {
        position: static;
    }
}
@media (min-height: 730px) and (max-height: 850px) and (max-width: 820px) {
    .bottom-layer {
        position: static;
    }
}
</style>
