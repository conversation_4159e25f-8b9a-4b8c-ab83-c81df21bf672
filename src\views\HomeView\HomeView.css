.home-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: auto;
  --home-button-color: rgb(42, 141, 255);
  --home-desc-text-color: rgb(155, 155, 155);
}

.home-one {
  width: 100%;
  position: relative;
}

.home-one-bg {
  width: 100%;
}

.home-one-text {
  position: absolute;
  top: 10%;
  left: 6%;
}

.home-one-text-title {
  font-size: 4dvw;
  color: var(--home-button-color);
  font-weight: bold;
  margin-bottom: 3dvh;
}

.home-one-text-subtitle {
  font-size: 2dvw;
  font-weight: bold;
  margin-bottom: 5dvh;
}

.home-one-text-desc {
  font-size: 1dvw;
  color: var(--home-desc-text-color);
}

.home-one-text-desc-p {
  font-size: 1dvw;
  color: var(--home-desc-text-color);
  margin-block: 2dvh;
}

.home-one-text-button {
  padding: 0.8dvw 2.5dvw;
  font-size: 1.1dvw;
  color: white;
  cursor: pointer;
  background: var(--home-button-color);
  border: none;
  transition: all 0.3s ease;
  margin-block: 4dvh 10dvh;
  padding: 1.5dvw 3dvw
}

.home-two {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 85dvh;
}

.home-two-text-title {
  font-size: 2dvw;
  font-weight: bold;
  margin-bottom: 3dvh;
  text-align: center;
}

.home-two-text-desc {
  font-size: 1dvw;
  color: var(--home-desc-text-color);
  margin-bottom: 5dvw;
}

.home-two-list {
  display: flex;
  gap: 2dvw;
}

.home-two-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgb(237, 244, 254);
  width: 14dvw;
  padding: 2dvw 2dvw;
  border-radius: 1dvw;
}

.home-two-box-icon-svg {
  padding: 1dvw;
  background: rgb(209, 227, 252);
  border-radius: 50%;
  margin-bottom: 2dvw;
}

.home-two-box-icon-svg {
  color: var(--home-button-color);
  font-size: 2.5dvw;
}

.home-two-box-title {
  font-size: 1.2dvw;
  font-weight: bold;
  margin-bottom: 2dvw;
}

.home-two-box-desc {
  font-size: 1dvw;
  font-weight: bold;
  color: var(--home-desc-text-color);
  text-align: center;
}

.home-three {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 85dvh;
  background: rgb(241, 246, 255);
  width: 100%;
}

.home-three-text-title {
  font-size: 2dvw;
  font-weight: bold;
  margin-bottom: 3dvh;
  text-align: center;
}

.home-three-carousel{
  width: 83%;
  margin-top: 5dvh;
}

.home-three-carousel-item{
  display: flex;
  padding-inline: 5%;
  gap: 3dvw;
  height: 50dvh;
}

.home-three-carousel-item-left{
  width: 50%;
}
.home-three-carousel-item-title{
  font-size: 1.5dvw;
  font-weight: bold;
}

.home-three-carousel-item-desc{
  font-size: 1dvw;
  font-weight: bold;
  padding-block: 2dvw;
}

.home-three-carousel-item-button-list{
  display: flex;
  gap: 2dvw;
}

.home-three-carousel-item-right{
  width: 50%;
  background: url("/login_bg.jpeg");
}