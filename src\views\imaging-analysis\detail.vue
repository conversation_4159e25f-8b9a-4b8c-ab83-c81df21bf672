<template>
    <div class="iar-report-box">
        <div class="title">影像助手</div>
        <div class="subtitle">欢迎使用AI影像助手，让智能分析为您的医学影像赋予全新洞察！</div>
        <div class="report-wrapper">
            <report v-if="isVisible" class="report" :id="props.id" />
        </div>
    </div>
</template>

<script setup>
import { useRoute } from 'vue-router';
import Report from './components/report.vue';
import { watch, ref, nextTick } from 'vue';
const route = useRoute();
const props = defineProps({
    id: {
        type: String,
        default: ''
    }
});
const isVisible = ref(true);
watch(route, (route) => {
    isVisible.value = false;
    nextTick(() => {
        isVisible.value = true;
    });
});
</script>

<style scoped lang="scss">
.iar-report-box {
    padding: 60px 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .title {
        text-align: center;
        font-size: 30px;
        font-weight: 500;
    }
    .subtitle {
        text-align: center;
        font-size: 16px;
        margin-top: 20px;
        color: #666;
        font-weight: 300;
    }
    .report-wrapper {
        min-height: 540px;
    }
    .report {
        margin: 20px auto;
        width: 600px;
        max-width: 100%;
        padding: 20px 0;
    }
}
.is-mobile {
    .iar-report-box {
        display: block;
    }
}
@media (max-height: 560px) {
    .iar-report-box {
        display: block;
    }
}
</style>
