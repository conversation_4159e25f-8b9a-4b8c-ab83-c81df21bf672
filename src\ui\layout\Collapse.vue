<template>
    <div class="rp-collapse">
        <div class="collapse-title">
            <div class="text">
                <slot name="title">{{ title }}</slot>
            </div>
            <el-icon class="toggle-icon" @click="toggle" v-if="!disabled">
                <component :is="isCollapsed ? 'ArrowDown' : 'ArrowUp'" />
            </el-icon>
        </div>
        <el-collapse-transition>
            <div class="collapse-content" v-show="!isCollapsed">
                <slot></slot>
            </div>
        </el-collapse-transition>
    </div>
</template>

<script setup>
import { ref } from 'vue';

const props = defineProps({
    disabled: {
        type: Boolean,
        default: false
    },
    title: {
        type: String,
        default: ''
    }
});
defineOptions({
    name: 'RpCollapse'
});
const isCollapsed = ref(false);

const toggle = () => {
    if (props.disabled) {
        return;
    }
    isCollapsed.value = !isCollapsed.value;
};
</script>

<style scoped lang="scss">
.rp-collapse {
    border: 1px solid #ddd;
    border-radius: 5px;
    background: #fff;
    + .rp-collapse {
        margin-top: 20px;
    }
    .collapse-title {
        background: #f5f5f5;
        border-radius: 5px 5px 0 0;
        font-weight: bold;
        font-size: 16px;
        display: flex;
        align-items: center;
        padding: 10px;
        .text {
            flex: 1;
        }
    }
    .toggle-icon {
        cursor: pointer;
    }
    .collapse-content {
        border-top: 1px solid #ddd;
        padding: 10px;
    }
}
</style>
