<template>
    <div class="chat-history">
        <!--<el-scrollbar> </el-scrollbar>-->
        <div class="title">聊天历史</div>
        <ul
            class="scroll-view"
            style="height: calc(100% - 40px); overflow-y: auto"
            v-infinite-scroll="nextPage"
            :infinite-scroll-disabled="loading || !hasMore"
        >
            <!--
            <li v-for="item in conversationList" :key="item.id">
                <chat-history-row :row="item" @click="handleRowChange(item)" />
            </li>
            -->
            <li class="time-group" v-for="(group, key) in conversationGroups" :key="key">
                <div class="time-title">{{ key }}</div>
                <ul class="chat-list">
                    <li v-for="row in group" :key="row.id" class="chat-item">
                        <chat-history-row :row="row" @click="handleRowChange(row)" />
                    </li>
                </ul>
            </li>
        </ul>
    </div>
</template>

<script setup>
import { onMounted, ref, defineEmits, computed, onUnmounted } from 'vue';
import { http } from '@/extensions/http';
import ChatHistoryRow from './ChatHistoryRow.vue';
import { groupBy } from 'lodash';
import { getTimeLabel } from '@/utils/index';
import { useEventBus } from '@vueuse/core';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import 'dayjs/locale/zh-cn';
dayjs.extend(relativeTime);
dayjs.locale('zh-cn');
const conversationList = ref([]);
const hasMore = ref(true);
const loading = ref(false);

const bus = useEventBus('chat');
const unsubscribe = bus.on((event) => {
    if (event === 'newChat') {
        refreshHistory();
    }
});

const emit = defineEmits(['change']);
onMounted(() => {
    refreshHistory();
});

onUnmounted(() => {
    unsubscribe();
});

const conversationGroups = computed(() => {
    return groupBy(conversationList.value, (item) => {
        // return dayjs(item.updated_at * 1000).fromNow();
        return getTimeLabel(item.updated_at * 1000);
    });
});

const handleRowChange = (row) => {
    emit('change', row);
};

const listHistoryConversation = async (lastId = '') => {
    if (!hasMore.value) {
        return Promise.resolve([]);
    }
    loading.value = true;
    return http.get('/wechat-api/chat/conversations', {
        params: {
            lastId: lastId,
            limit: 50
        }
    }, {
        local: true
    }).then(res => {
        const json = res.data;
        hasMore.value = json.has_more;
        return json.data;
    }).finally(() => {
        loading.value = false;
    });
};
const refreshHistory = async () => {
    hasMore.value = true;
    const conversations = await listHistoryConversation();
    conversationList.value = conversations;
};

const nextPage = async () => {
    console.log('next page');
    if (loading.value) {
        return;
    }
    if (conversationList.value[conversationList.value.length - 1]) {
        const lastId = conversationList.value[conversationList.value.length - 1].id;
        const conversations = await listHistoryConversation(lastId);
        conversationList.value.push(...conversations);
    }
};

</script>

<style lang="scss" scoped>
.chat-history {
    flex: 1;
    overflow: hidden;
    .title {
        padding: 8px 16px;
        height: 40px;
    }
    .scroll-view {
        padding: 0 8px;
    }
    .time-group {
        position: relative;
        margin-bottom: 8px;
        .time-title {
            font-size: 13px;
            font-weight: 500;
            color: #000;
            padding: 0 8px;
            position: sticky;
            top: 0;
            z-index: 10;
            background: #f9fbff;
        }
    }
    .chat-list {}
    .history-item {
        padding: 8px 16px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        cursor: pointer;

        &:hover {
            background: #e6e6e6;
        }

        .history-item-content {
            display: flex;
            align-items: center;
            gap: 8px;
            flex: 1;

            .title {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }
}
</style>
