<template>
    <div class="vma-create">
        <div class="title">开始问诊</div>
        <div class="subtitle">点击下方按钮开始记录您的问诊过程，系统将自动转录为文字并创建病历</div>
        <voice-record ref="voiceRecordRef" class="recorder" @end="handleRecordEnd" />
    </div>
</template>

<script setup>
import VoiceRecord from './components/VoiceRecord.vue';
import { useRouter } from 'vue-router';
import { useRoute } from 'vue-router';
import { ref, watch } from 'vue';
import { useEventBus } from '@vueuse/core';
const bus = useEventBus('voiceMedicalRecord');

const router = useRouter();
const route = useRoute();
const voiceRecordRef = ref(null);

watch(() => route.query, (newVal) => {
    if (voiceRecordRef.value) {
        voiceRecordRef.value.stopRecording(true);
    }
});

const handleRecordEnd = (data) => {
    console.log('handleRecordEnd', data);
    bus.emit('newRecord', data);
    router.push({
        name: 'VmaDetail',
        params: {
            id: data.id
        }
    });
};

</script>

<style scoped lang="scss">
.vma-create {
    padding: 60px 20px;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .title {
        text-align: center;
        font-size: 30px;
        font-weight: 500;
    }
    .subtitle {
        text-align: center;
        font-size: 16px;
        margin-top: 20px;
        color: #666;
        font-weight: 300;
    }
    .recorder {
        margin: 20px auto;
        width: 600px;
        max-width: 100%;
        padding: 20px 0;
    }
}
@media (max-height: 560px) {
    .vma-create {
        display: block;
    }
}
</style>
