<template>
    <div class="user-center">
        <el-dropdown placement="top-start" trigger="click" style="display: block">
            <div class="uc">
                <rp-icon name="user" size="20" />
                <span>{{ maskPhoneNumber(user.mobile) }}</span>
            </div>
            <template #dropdown>
                <el-dropdown-menu>
                    <!--
                    <el-dropdown-item>
                        <div class="menu-item">
                            <rp-icon name="guide" size="20" />
                            <span>关于我们</span>
                        </div>
                    </el-dropdown-item>
                    -->
                    <el-dropdown-item>
                        <div class="menu-item" @click="logout">
                            <rp-icon name="back" size="20" />
                            <span>退出登录</span>
                        </div>
                    </el-dropdown-item>
                </el-dropdown-menu>
            </template>
        </el-dropdown>
    </div>
</template>

<script setup>
import { useUserStore } from '@/stores/user';
import { computed } from 'vue';
import { maskPhoneNumber } from '@/utils/index';

const userStore = useUserStore();

const user = computed(() => {
    return userStore.getUser();
});

const logout = async () => {
    await userStore.logout();
    window.location.href = '/';
};
</script>

<style scoped lang="scss">
.user-center {
    padding: 8px;
    border-top: 1px solid #e1effd;
}
.uc {
    display: flex;
    align-items: center;
    padding: 8px;
    border-radius: var(--el-border-radius-base);
    span {
        margin-left: 10px;
    }
    &:hover {
        cursor: pointer;
        background: #e1effd;
    }
}
.menu-item {
    width: 140px;
    display: flex;
    align-items: center;
}
</style>
