<template>
    <component :is="compiledVNode" />
</template>

<script setup>
import { computed } from 'vue';
import { useMarkdownRenderer } from '@/hooks/useMarkdown.js';
import { compile } from 'vue';
import '@/styles/markdown.scss';

const props = defineProps({
    content: {
        type: String,
        default: ''
    }
});
const htmlTags = [
    'a', 'abbr', 'address', 'area', 'article', 'aside', 'audio',
    'b', 'base', 'bdi', 'bdo', 'blockquote', 'br', 'button',
    'canvas', 'caption', 'cite', 'code', 'col', 'colgroup',
    'data', 'datalist', 'dd', 'del', 'details', 'dfn', 'dialog', 'div', 'dl', 'dt',
    'em', 'embed',
    'fieldset', 'figcaption', 'figure', 'footer', 'form',
    'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'header', 'hr',
    'i', 'img', 'input', 'ins',
    'kbd',
    'label', 'legend', 'li', 'link',
    'main', 'map', 'mark', 'meta', 'meter',
    'nav', 'noscript',
    'object', 'ol', 'optgroup', 'option', 'output',
    'p', 'param', 'picture', 'pre', 'progress',
    'q',
    'rp', 'rt', 'ruby',
    's', 'samp', 'section', 'select', 'small', 'source', 'span', 'strong', 'sub', 'summary', 'sup',
    'table', 'tbody', 'td', 'template', 'textarea', 'tfoot', 'th', 'thead', 'time', 'title', 'tr', 'track',
    'u', 'ul',
    'var', 'video',
    'wbr',
    'llm-mark', 'llm-think', 'rp-icon', 'llm-cite'
];
/**
 * 将html内容中缺失的标签补充完整
 * @param html
 */
function makeHtmlComplete(html) {
    if (!html) {
        return '';
    }
    // 使用DOMParser自动修复HTML标签闭合问题
    const parser = new DOMParser();
    const doc = parser.parseFromString(html, 'text/html');
    return doc.body.innerHTML;
}
function escapeHtml(html, retainTags = []) {
    const tagRegex = /<\/?([a-zA-Z][a-zA-Z0-9\-]*)(\s[^>]*)?>/g;
    const placeholderMap = {};
    let index = 0;

    // 替换 retainTags 中的合法标签为占位符
    html = html.replace(tagRegex, (match, tagName) => {
        if (retainTags.includes(tagName)) {
            const key = `%%PLACEHOLDER_${index++}%%`;
            placeholderMap[key] = match;
            return key;
        }
        else {
            return match;
        }
    });

    // 然后转义剩下的所有内容（只转义 &，<，>，" 和 '）
    html = html.replace(/<(\/?[a-zA-Z]+[^>]*)>/g, '&lt;$1&gt;');

    // 恢复占位符中合法标签
    for (const [key, tag] of Object.entries(placeholderMap)) {
        html = html.replace(key, tag);
    }

    return html;
}

const md = useMarkdownRenderer();

const htmlContent = computed(() => {
    return makeHtmlComplete(escapeHtml(md.render(props.content), htmlTags));
    // return makeHtmlComplete(md.render(props.content), htmlTags);
});

// 将 HTML 编译为 VNode，同时让 Vue 能识别组件
const compiledVNode = computed(() => {
    return compile(`<markdown-container>${htmlContent.value}</markdown-container>`, {});
});
</script>
