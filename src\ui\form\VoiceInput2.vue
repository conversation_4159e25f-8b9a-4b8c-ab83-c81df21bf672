<template>
    <div class="voice-input">
        <el-input
            ref="inputRef"
            v-model="currentValue"
            :type="props.type"
            :disabled="props.disabled"
            :placeholder="props.placeholder"
            :autosize="{
                minRows: 2,
                maxRows: 6
            }" style="width: 100%;" :rows="3"
            @input="handleInputChange"
        />
        <div class="cover">
            {{ inputSelectionValue.prev }}<span class="new-value">{{ voiceText.join('') }}</span>{{ inputSelectionValue.next }}
        </div>
        <div class="btn">
            <div v-if="isRecording" @click="() => stopRecording(false)" class="item">
                <rp-icon color="var(--el-color-danger)" name="icon-luyin-stop" size="16" />
            </div>
            <div v-else @click.stop.prevent="startRecording" class="item">
                <rp-icon name="icon-luyin" size="16" />
            </div>
        </div>
        <div class="wave">
            <canvas ref="canvasRef" class="wave-canvas"></canvas>
        </div>
    </div>
</template>

<script setup>
import SpeechTranscription from '@/extensions/lib/SpeechTranscription';
import { computed, nextTick, onMounted, onUnmounted, ref, unref, watch } from 'vue';
import RpIcon from '@/ui/icon/icon.vue';

const inputRef = ref(null);
const inputSelectionIndex = ref(0);
const canvasRef = ref(null);
const audioDeviceId = ref(null);

const isRecording = ref(false);
// 录音任务是否已取消
const isCanceled = ref(false);

let canvasCtx = null;
let animationFrameId = null;

const props = defineProps({
    modelValue: {
        type: String,
        default: ''
    },
    type: {
        type: String,
        default: 'textarea'
    },
    disabled: {
        type: Boolean,
        default: false
    },
    placeholder: {
        type: String,
        default: ''
    }
});

const currentValue = ref('');
const beforeValue = ref('');
const emit = defineEmits(['start', 'end', 'update:modelValue']);
const inputSelectionValue = computed(() => {
    const text = beforeValue.value || '';
    return {
        prev: text.substring(0, inputSelectionIndex.value),
        next: text.substring(inputSelectionIndex.value),
    };
});
watch(() => props.modelValue, () => {
    currentValue.value = props.modelValue;
});
let audioContext;
let audioInput;
let audioStream;
let workletNode;
let analyser;
const voiceText = ref([]);
const st = new SpeechTranscription();

st.on('started', (msg) => {
    console.log('Client recv started');
    emit('start');
});

st.on('changed', (msg) => {
    const payload = JSON.parse(msg).payload;
    voiceText.value.splice(payload.index - 1, 1, payload.result);
    const text = voiceText.value.join('');
    currentValue.value = inputSelectionValue.value.prev + text + inputSelectionValue.value.next;
});

st.on('completed', (msg) => {
    console.log('Client recv completed:', msg);
});

st.on('begin', (msg) => {
    // console.log('Client recv sentenceBegin:', msg);
});

st.on('end', (msg) => {
    // console.log('Client recv sentenceEnd:', msg);
    const payload = JSON.parse(msg).payload;
    voiceText.value.splice(payload.index - 1, 1, payload.result);
    const text = voiceText.value.join('');
    currentValue.value = inputSelectionValue.value.prev + text + inputSelectionValue.value.next;
});

st.on('closed', async () => {
    console.log('Client recv closed');
    if (isCanceled.value) {
        return;
    }
    // 尝试停止录音，释放资源
    stopRecording();
    const text = unref(voiceText).join('');
    console.log('识别结果:', text);
    currentValue.value = inputSelectionValue.value.prev + text + inputSelectionValue.value.next;
    emit('update:modelValue', currentValue.value);
    voiceText.value = [];
});

st.on('failed', (msg) => {
    console.log('Client recv failed:', msg);
    // todo 识别异常处理
});
onMounted(() => {
});
onUnmounted(() => {
    stopRecording(); // 页面卸载自动释放资源
});
let waveformData = null; //  new Float32Array(512); // 存储最新的音频帧数据
const audioChunks = []; // 全局变量，收集音频块
const drawWaveform = () => {
    if (!canvasCtx) return;

    analyser.getByteTimeDomainData(waveformData); // 获取时域数据

    const width = canvasRef.value.width;
    const height = canvasRef.value.height;
    canvasCtx.clearRect(0, 0, width, height);

    // 渐变背景（视觉柔和）
    canvasCtx.fillStyle = 'rgba(255, 255, 255, 0)';
    canvasCtx.fillRect(0, 0, width, height);

    // 创建波形线条渐变
    const gradient = canvasCtx.createLinearGradient(0, 0, width, 0);
    gradient.addColorStop(0, 'rgba(236,127,22,0)');
    gradient.addColorStop(0.5, '#ec7f16');
    gradient.addColorStop(1, 'rgba(236,127,22,0)');

    canvasCtx.lineWidth = 2;
    canvasCtx.strokeStyle = gradient;
    canvasCtx.beginPath();

    const sliceWidth = width / waveformData.length;
    let x = 0;
    // 整体振幅提升因子
    const amplitudeBoost = 1;

    canvasCtx.beginPath();

    for (let i = 0; i < waveformData.length; i++) {
        const magnitude = waveformData[i];
        const y = height - (magnitude / 255) * height; // 反向绘制顶部为 255

        if (i === 0) {
            canvasCtx.moveTo(x, y);
        }
        else {
            canvasCtx.lineTo(x, y);
        }

        x += sliceWidth;
    }

    canvasCtx.strokeStyle = gradient;
    canvasCtx.lineWidth = 2;
    canvasCtx.stroke();

    animationFrameId = requestAnimationFrame(drawWaveform);
};

const startRecording = async () => {
    if (isRecording.value) {
        return;
    }
    beforeValue.value = currentValue.value;
    inputSelectionIndex.value = inputRef.value[props.type].selectionStart;

    isCanceled.value = false;
    // 启动WebSocket
    await st.start(st.defaultStartParams());

    isRecording.value = true;
    voiceText.value = [];
    audioChunks.splice(0);

    try {
        // 获取音频输入设备
        audioStream = await navigator.mediaDevices.getUserMedia({
            audio: {
                noiseSuppression: true,     // 启用降噪
                echoCancellation: true,     // 回声消除
                autoGainControl: true,       // 自动增益控制
                deviceId: audioDeviceId.value ? {
                    exact: audioDeviceId.value
                } : undefined
            }
        });
        audioContext = new (window.AudioContext || window.webkitAudioContext)();
        audioInput = audioContext.createMediaStreamSource(audioStream);

        // 加载 worklet js 文件
        // await audioContext.audioWorklet.addModule('/src/views/vma/components/recorder-worklet.js');
        await audioContext.audioWorklet.addModule('/recorder-worklet.js');
        workletNode = new AudioWorkletNode(audioContext, 'recorder-worklet', {
            processorOptions: {
                sampleRate: audioContext.sampleRate
            }
        });
        workletNode.port.onmessage = (event) => {
            const buffer = event.data;
            const int16Buffer = new Int16Array(buffer);
            audioChunks.push(int16Buffer); // 收集
            st.sendAudio(buffer);
        };

        analyser = audioContext.createAnalyser();
        analyser.fftSize = 512;
        waveformData = new Uint8Array(analyser.frequencyBinCount);

        audioInput.connect(analyser);
        analyser.connect(workletNode);
        // audioInput.connect(workletNode);
        // workletNode.connect(audioContext.destination);
        canvasCtx = canvasRef.value.getContext('2d');
        drawWaveform();
        console.log('🎙️ AudioWorklet 已启动');
    }
    catch (e) {
        console.log('录音失败: ' + e);
        isRecording.value = false;
    }
};

const stopRecording = (cancel = false) => {
    if (!isRecording.value) {
        return;
    }
    isCanceled.value = cancel;
    if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
        animationFrameId = null;
    }

    if (workletNode) {
        workletNode.disconnect();
        workletNode.port.onmessage = null;
        workletNode = null;
    }
    if (audioInput) {
        audioInput.disconnect();
        audioInput = null;
    }
    if (audioStream) {
        audioStream.getTracks().forEach(track => track.stop());
        audioStream = null;
    }
    if (audioContext) {
        audioContext.close();
        audioContext = null;
    }
    st.close();
    if (canvasCtx && canvasRef.value) {
        canvasCtx.clearRect(0, 0, canvasRef.value.width, canvasRef.value.height);
    }
    setTimeout(() => {
        isRecording.value = false;
    }, 1000);

    console.log('🛑 录音停止，波形图清空');
};
const handleInputChange = (value) => {
    emit('update:modelValue', value);
};

defineExpose({
    startRecording,
    stopRecording
});
</script>

<style scoped lang="scss">
.voice-input {
    position: relative;
    overflow: hidden;
    width: 100%;
    .cover {
        pointer-events: none;
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        padding: 5px 11px;
        line-height: 1.5;
        color: transparent;
        white-space: pre-wrap;
        .new-value {
            color: #acacac;
        }
    }
    .btn {
        position: absolute;
        right: 0px;
        bottom: 0px;
        cursor: pointer;
        .item {
            padding: 8px;
            line-height: 1;
        }
    }
    .wave {
        pointer-events: none;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
    }
    .wave-canvas {
        width: 100%;
        height: 100%;
        //height: 50px;
        //background: #1D78EB;
    }
}
</style>
