function downsampleBuffer(buffer, originalRate, targetRate) {
    if (targetRate === originalRate) return buffer;
    const sampleRateRatio = originalRate / targetRate;
    const newLength = Math.round(buffer.length / sampleRateRatio);
    const result = new Float32Array(newLength);

    let offset = 0;
    for (let i = 0; i < newLength; i++) {
        const nextOffset = Math.round((i + 1) * sampleRateRatio);
        let sum = 0;
        let count = 0;
        for (let j = offset; j < nextOffset && j < buffer.length; j++) {
            sum += buffer[j];
            count++;
        }
        result[i] = count > 0 ? sum / count : 0;
        offset = nextOffset;
    }

    return result;
}

class RecorderWorkletProcessor extends AudioWorkletProcessor {
    constructor(options) {
        super();
        this.sampleRate = options.processorOptions.sampleRate || 48000;
    }

    process(inputs) {
        const input = inputs[0]; // 获取输入流
        if (input && input[0]) {
            const channelData = input[0];
            // 拷贝数据防止被复用
            const clonedBuffer = new Float32Array(channelData.length);
            clonedBuffer.set(channelData);

            const float32 = downsampleBuffer(clonedBuffer, this.sampleRate, 16000);
            const int16 = new Int16Array(float32.length);
            for (let i = 0; i < float32.length; i++) {
                const s = Math.max(-1, Math.min(1, float32[i]));
                int16[i] = s < 0 ? s * 0x8000 : s * 0x7FFF;
            }
            this.port.postMessage(int16.buffer); // 发送到主线程
        }
        return true;
    }
}

registerProcessor('recorder-worklet', RecorderWorkletProcessor);
