@use "sass:color";
// 重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
}
// 全局变量
:root {
  --primary-color: #0068b7;
  --primary-hover-color: #66b1ff;
  --success-color: #67C23A;
  --warning-color: #E6A23C;
  --danger-color: #F56C6C;
  --info-color: #909399;
  --text-color: #303133;
  --border-color: #DCDFE6;
}

// 全局样式
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  color: var(--text-color);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 链接样式
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color 0.3s ease;

  &:hover {
    color: var(--primary-hover-color);
  }
}
img {
  -webkit-user-drag: none;
  user-select: none;
}
// 容器样式
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
.el-drawer__header {
  margin-bottom: 10px !important;
}
.el-dropdown__popper .el-dropdown-menu {
  padding: 5px !important;
}
.el-dropdown-menu__item {
  border-radius: var(--el-border-radius-base);
}
.el-drawer, .el-dialog {
  max-width: 100%;
}
