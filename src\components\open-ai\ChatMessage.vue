<template>
    <div class="chat-message">
        <div class="message query">
            <div class="message-content">
                <div class="marked">
                    <markdown-renderer :content="currentMessage.query" />
                </div>
            </div>
        </div>
        <div class="message answer">
            <!--
            <div class="avatar">
                <img src="https://img1.rpfieldcdn.com/ai/vet/logo.png" width="40" alt="">
            </div>
            -->
            <div class="message-content">
                <div class="think" v-if="currentMessage.think">
                    <md-think :status="currentMessage.status">
                        <div class="marked">
                            <markdown-renderer :content="currentMessage.think" />
                        </div>
                    </md-think>
                </div>
                <markdown-renderer class="marked" :content="currentMessage.content" />
                <template v-if="currentMessage.status===1 && currentMessage.content">
                    <div class="actions">
                        <div class="action">
                            <el-tooltip effect="dark" content="复制" placement="top" :show-after="200">
                                <rp-icon name="icon-copy" @click="copyAnswer" />
                            </el-tooltip>
                        </div>
                        <div class="action">
                            <el-tooltip effect="dark" content="喜欢" placement="top" :show-after="200">
                                <rp-icon
                                    name="icon-zan"
                                    @click="putMessageFeedback('like')"
                                    :color="currentMessage.rating==='like' ? 'var(--el-color-primary)' : '#333'"
                                />
                            </el-tooltip>
                        </div>
                        <div class="action">
                            <el-tooltip effect="dark" content="不喜欢" placement="top" :show-after="200">
                                <rp-icon
                                    name="icon-cai"
                                    @click="putMessageFeedback('dislike')"
                                    :color="currentMessage.rating==='dislike' ? 'var(--el-color-error)' : '#333'"
                                />
                            </el-tooltip>
                        </div>
                    </div>
                    <div class="tips">以上内容由大模型生成，仅供参考</div>
                </template>
            </div>
        </div>
    </div>
</template>

<script setup>
import MarkdownRenderer from '@/components/markdown/MarkdownRenderer.vue';
import { ref, unref, watchEffect } from 'vue';
import MdThink from '@/components/chat-widgets/think.vue';
import { ElMessageBox } from 'element-plus';
import { copyText } from '@/utils/index.js';
import { http } from '@/extensions/http.js';
import { httpApis } from '@/hooks/useHttpApi.js';
import { useToast } from '@/hooks/useToast.js';
const toast = useToast();
const thinkRef = ref(null);
const props = defineProps({
    message: {
        type: Object,
        required: true
    }
});
const currentMessage = ref({
    query: '',
    think: '',
    content: '',
    status: 0
});
const putMessageFeedback = rating => {
    let ratingValue = rating;
    if (currentMessage.value.rating === rating) {
        ratingValue = null;
    }
    if (ratingValue !== 'dislike') {
        saveMessageFeedback({
            messageId: currentMessage.value.messageId,
            rating: ratingValue,
            content: ''
        });
        return;
    }
    ElMessageBox.prompt('请告诉我不对的地方，以便更好的进行改进', '意见反馈', {
        confirmButtonText: '提交反馈',
        cancelButtonText: '取消',
    }).then(({
        value
    }) => {
        saveMessageFeedback({
            messageId: currentMessage.value.messageId,
            rating: ratingValue,
            content: value
        });
    });
};
/**
 * 拷贝内容到剪切板
 */
const copyAnswer = () => {
    // 移除think过程
    const content = currentMessage.value.answer
        .replace(/<think[^>]*>[\s\S]*<\/think>/g, '')
        .replace(/<llm-think[^>]*>[\s\S]*<\/llm-think>/g, '');
    if (copyText(content)) {
        toast.success('复制成功');
    }
};
/**
 * 保存反馈信息
 * @param data
 */
const saveMessageFeedback = (data) => {
    http.post(httpApis.putMedicalRecordMessageFeedback, data).then(() => {
        currentMessage.value.rating = data.rating;
    });
};
watchEffect(() => {
    const message = unref(props.message);
    // const message = {
    //     query: '',
    //     think: '',
    //     content: '',
    //     status: 0
    // };
    const isDone = message.status === 1;
    if (isDone && thinkRef.value) {
        thinkRef.value.hideContent();
    }
    const text = message.answer;
    // 拆分answer为think+content
    if (/^\s*<think>/.test(text)) {
        const idx = text.indexOf('</think>');
        const startIdx = text.indexOf('<think>') + 7;
        if (idx === -1) {
            message.think = text.slice(startIdx);
            message.content = '';
        }
        else {
            message.think = text.slice(startIdx, idx);
            message.content = text.slice(idx + 8);
        }
    }
    else {
        message.content = text;
    }
    message.content = message.content.replace(/^\s+/g, '')
        // .replace(/<\/?\w{0,5}$/g, '') // 避免不完整的mark,think标签展示出来
        .replace(/<\/?([a-zA-Z]{1,4})[^>]*$/g, (a, b) => {
            if (b.indexOf('mark') === 0 || b.indexOf('cite') === 0) {
                return '';
            }
            return a;
        }) // 避免不完整的mark,think标签展示出来
        .replace(/<(mark|think|cite)/g, '<llm-$1')
        .replace(/<\/(mark|think|cite)>/g, '</llm-$1>');

    if (!text && !props.isDone.value) {
        message.content = '正在思考中……';
    }
    currentMessage.value = message;
});
</script>

<style scoped lang="scss">
.chat-message {
    font-size: 13px;
    margin: 0 20rpx;
    word-break: break-word;
    overflow-wrap: break-word;
    word-wrap: break-word;
    white-space: normal;
    .message {
        font-size: 14px;
        display: flex;
        padding: 10rpx 0;
        margin-bottom: 10px;
        .avatar {
            //width: 70rpx;
            //height: 70rpx;
            font-size: 14px;
            color: #555;
            margin-bottom: 4px;
            line-height: 25px;
            img {
                border: 1px solid #ddd;
                border-radius: 50%;
            }
        }
        .message-content {
            .footer {
                display: flex;
                align-items: center;
                margin-top: 5px;
            }
            .tips {
                border-top: 1px solid #ddd;
                margin-top: 5px;
                flex: 1;
                font-size: 11px;
                color: #999;
            }
            .actions {
                .action-item {
                    margin: 0 10px;
                    &.active {
                        color: var(--el-color-primary);
                    }
                }
            }
        }
        &.query {
            justify-content: flex-end;
            .message-content {
                max-width: 80%;
                background-color: #e9f0fb; // var(--el-color-primary);
                //color: #fff;
                border-radius: 10px;
                padding: 8px;
            }
        }
        &.answer {
            //justify-content: flex-start;
            display: flex;
            background: #fff;
            padding: 8px 0;
            border-radius: 10px;
            .message-content {
                max-width: 100%;
                flex: 1;
                margin-left: 10px;
            }
        }
        .actions {
            display: flex;
            gap: 15px;
            align-items: center;
            .action {
                cursor: pointer;
            }
        }
    }
}
</style>
