import SpeechTranscription from '@/extensions/lib/SpeechTranscription';
import { unref, ref } from 'vue';
const isVoiceBusy = ref(false);
export const useVoiceRecord = (options = {}) => {
    const voiceText = ref([]);
    const isCanceled = ref(false);
    const isRecording = ref(false);
    const st = new SpeechTranscription();
    let audioContext;
    let audioInput;
    let audioStream;
    let workletNode;
    let analyser;
    const audioChunks = []; // 全局变量，收集音频块

    st.on('started', (msg) => {
        console.log('Client recv started');
        if (typeof options.onVoiceStarted === 'function') {
            options.onVoiceStarted(msg);
        }
    });

    st.on('changed', (msg) => {
        const payload = JSON.parse(msg).payload;
        voiceText.value.splice(payload.index - 1, 1, payload.result);
        const text = voiceText.value.join('');
        if (typeof options.onVoiceTextChanged === 'function') {
            options.onVoiceTextChanged(text);
        }
    });

    st.on('completed', (msg) => {
        console.log('Client recv completed:', msg);
        if (typeof options.onVoiceCompleted === 'function') {
            options.onVoiceCompleted(voiceText.value.join(''), msg);
        }
    });

    st.on('begin', (msg) => {
        // console.log('Client recv sentenceBegin:', msg);
    });

    st.on('end', (msg) => {
        console.log('Client recv sentenceEnd:', msg);
        const payload = JSON.parse(msg).payload;
        voiceText.value.splice(payload.index - 1, 1, payload.result);
        const text = voiceText.value.join('');
        if (typeof options.onVoiceEnd === 'function') {
            options.onVoiceEnd(text);
        }
    });

    st.on('closed', async () => {
        console.log('Client recv closed');
        if (isCanceled.value) {
            return;
        }
        // 尝试停止录音，释放资源
        stopRecording();
        const text = unref(voiceText).join('');
        console.log('识别结果:', text);
        if (typeof options.onVoiceClosed === 'function') {
            options.onVoiceClosed(text, audioChunks);
        }
    });

    st.on('failed', (msg) => {
        console.log('Client recv failed:', msg);
        if (typeof options.onVoiceFailed === 'function') {
            options.onVoiceFailed(msg);
        }
    });

    const startRecording = async () => {
        if (isRecording.value) {
            return;
        }
        isCanceled.value = false;
        if (typeof options.beforeStartRecording === 'function') {
            options.beforeStartRecording();
        }
        // 启动WebSocket
        await st.start(st.defaultStartParams());

        voiceText.value = [];
        audioChunks.splice(0);

        try {
            // 获取音频输入设备
            audioStream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    noiseSuppression: true,     // 启用降噪
                    echoCancellation: true,     // 回声消除
                    autoGainControl: true,       // 自动增益控制
                    deviceId: options.audioDeviceId.value ? {
                        exact: options.audioDeviceId.value
                    } : undefined
                }
            });
            audioContext = new (window.AudioContext || window.webkitAudioContext)();
            audioInput = audioContext.createMediaStreamSource(audioStream);

            // 加载 worklet js 文件
            // await audioContext.audioWorklet.addModule('/src/views/vma/components/recorder-worklet.js');
            await audioContext.audioWorklet.addModule('/recorder-worklet.js');
            workletNode = new AudioWorkletNode(audioContext, 'recorder-worklet', {
                processorOptions: {
                    sampleRate: audioContext.sampleRate
                }
            });
            workletNode.port.onmessage = (event) => {
                const buffer = event.data;
                const int16Buffer = new Int16Array(buffer);
                audioChunks.push(int16Buffer); // 收集
                st.sendAudio(buffer);
            };

            analyser = audioContext.createAnalyser();
            analyser.fftSize = 512;
            // const waveformData = new Uint8Array(analyser.frequencyBinCount);

            audioInput.connect(analyser);
            analyser.connect(workletNode);
            // canvasCtx = canvasRef.value.getContext('2d');
            // drawWaveform();
            if (typeof options.drawWave === 'function') {
                options.drawWave(analyser);
            }
            console.log('🎙️ AudioWorklet 已启动');
            isRecording.value = true;
            isVoiceBusy.value = true;
        }
        catch (e) {
            console.log('录音失败: ' + e);
            console.error(e);
            isRecording.value = false;
            isVoiceBusy.value = false;
        }
    };
    const stopRecording = async (cancel = false) => {
        if (!isRecording.value) {
            return;
        }
        isCanceled.value = cancel;

        if (workletNode) {
            workletNode.disconnect();
            workletNode.port.onmessage = null;
            workletNode = null;
        }
        if (audioInput) {
            audioInput.disconnect();
            audioInput = null;
        }
        if (audioStream) {
            audioStream.getTracks().forEach(track => track.stop());
            audioStream = null;
        }
        if (audioContext) {
            audioContext.close();
            audioContext = null;
        }
        st.close();

        setTimeout(() => {
            isRecording.value = false;
            isVoiceBusy.value = false;
        }, 1000);

        console.log('🛑 录音停止，波形图清空');
    };

    return {
        startRecording,
        stopRecording,
        voiceText,
        isRecording,
        isVoiceBusy,
        isCanceled,
    };
};
