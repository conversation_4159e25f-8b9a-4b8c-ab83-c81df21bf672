const apis = {
    sendSmsCode: '/wechat-api/thirdparty/sms/send-verify-code',
    loginByMobile: '/wechat-api/wechat/user/login-by-mobile',
    logout: '/wechat-api/wechat/user/logout',
    getLoginInfo: '/wechat-api/wechat/user/get-login-info',
    getCosSts: '/wechat-api/thirdparty/cos/sts',
    // Chat API
    sendChatMessage: '/wechat-api/chat/chat-messages',
    loadChatMessagesByConversationId: '/wechat-api/chat/messages',
    stopChatMessage: '/wechat-api/chat/chat-messages/stop',
    putMessageFeedback: '/wechat-api/chat/messages/feedback',
    recognizeLaboratoryReport: '/wechat-api/chat-tools/laboratory/image-recognize',
    recognizeImageReport: '/wechat-api/chat-tools/image/image-recognize',
    // 语音病历
    createVoiceMedicalRecord: '/wechat-api/his/medical-record/voice/create',
    updateMedicalRecord: '/wechat-api/his/medical-record/update',
    listMyMedicalRecord: '/wechat-api/his/medical-record/my',
    getMedicalRecord: '/wechat-api/his/medical-record/get',
    medicalRecordChat: '/wechat-api/his/ai-message/chat-messages',
    getMedicalRecordChatMessages: '/wechat-api/his/ai-message/get-list/:medicalId',
    putMedicalRecordMessageFeedback: '/wechat-api/his/ai-message/feedback',
    createLabReport: '/wechat-api/his/medical-report/create',
    deleteLabReport: '/wechat-api/his/medical-report/delete/:id',
    getLabReportByMedicalRecord: '/wechat-api/his/medical-report/get-by-medical-record-id',
    // 影像助手
    getImagingAnalysisReport: '/wechat-api/his/imaging-analysis-report/get',
    listMyImagingAnalysisReports: '/wechat-api/his/imaging-analysis-report/my',
    createImagingAnalysisReport: '/wechat-api/vet1/xray/analyze',
    // SDK
    getNlsAccessToken: '/wechat-api/thirdparty/alibaba/nls/get-access-token'
};

export default apis;
