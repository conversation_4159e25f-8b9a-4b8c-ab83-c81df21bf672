import api from "@/utils/api";

export interface AliyunTokenResponse {
    token: string;
    appKey: string;
    expireTime: number;
}

// 宠物性别枚举
export const PetGender = {
    MALE: "MALE",
    FEMALE: "FEMALE",
    UNKNOWN: "UNKNOWN",
} as const;
export type PetGender = typeof PetGender[keyof typeof PetGender];

// 宠物品种枚举
export const PetSpecies = {
    CAT: "CAT",
    DOG: "DOG",
    OTHER: "OTHER"
}
export type PetSpecies = typeof PetGender[keyof typeof PetGender];

// 宠物绝育的枚举
export const PetIsNeutered = {
    YES: "YES",
    NO: "NO",
    UNKNOWN: "UNKNOWN"
}
export type PetIsNeutered = typeof PetIsNeutered[keyof typeof PetIsNeutered];

export interface UploadedFileInfo {
  cos_path: string;
  file_id: number;
  file_name: string;
  file_size: number;
  upload_time: string;
  url: string;
}

// 病历记录基础模型接口
export interface VoiceMedicalRecord {
    // 基本信息
    id?: number;
    pet_name?: string;
    pet_gender?: PetGender | null;
    pet_age_years?: number | null;
    pet_age_months?: number | null;
    pet_is_neutered?: PetIsNeutered | null;
    pet_species?: PetSpecies | null;
    pet_breed?: string | null;

    // 主诉和病史
    chief_complaint?: string | null;
    past_history?: string | null;
    present_illness?: string | null;

    // 体征数据
    pet_weight?: number | null;
    pet_temperature?: number | null;
    pet_heart_rate?: number | null;
    pet_respiratory_rate?: number | null;
    body_desc?: string | null;

    // 诊断和治疗
    diagnosis?: string | null;
    treatment?: string | null;
    advice?: string | null;

    // 客观检查
    inspection_report_ids?: number[] | null;
    image_report_ids?: number[] | null;
    inspection_report_files?: UploadedFileInfo[] | null;
    image_report_files?: UploadedFileInfo[] | null;
}


/**
 * 语音服务类
 */
export class VoiceService {
    /**
     * 获取阿里云Token
     *
     */
    static async getAliyunToken(): Promise<AliyunTokenResponse> {
        const response = await api.get<any>("/api/voice/get-aliyun-token");
        return {
            token: response.token,
            appKey: response.app_key,
            expireTime: response.expire_time
        };
    }

    /**
     * 语音文字直接转成病历
     *
     */
    static async createVoiceMedicalRecordFormApi(voiceText: string): Promise<VoiceMedicalRecord> {
        const medicalRecord = await api.post<VoiceMedicalRecord>("/api/voice-medical-records/voice-text-to-medical-record", {
            voice_text: voiceText
        });
        return await api.post<any>("/api/voice-medical-records", medicalRecord);
    }

    /**
 * 修改病历信息
 *
 */
    static async getVoiceMedicalRecordFormApi(id: number): Promise<VoiceMedicalRecord> {
        return await api.get<VoiceMedicalRecord>(`/api/voice-medical-records/${id}`);
    }

    /**
     * 修改病历信息
     *
     */
    static async updateVoiceMedicalRecordFormApi(medicalRecord: VoiceMedicalRecord): Promise<VoiceMedicalRecord> {
        return await api.put<VoiceMedicalRecord>(`/api/voice-medical-records/${medicalRecord.id}`, medicalRecord);
    }
}

export default VoiceService;