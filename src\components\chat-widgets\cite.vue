<template>
    <el-popover
        placement="top"
        width="300"
        trigger="hover"
    >
        <div class="llm-cite-content">
            <div class="header">参考文献</div>
            <div class="text">
                <div class="title">
                    {{ props.title }}
                </div>
                <div class="marked content" v-if="citeMeta.content" v-html="citeMeta.content"></div>
            </div>
        </div>
        <template #reference>
            <span class="llm-cite">
                {{ citeMeta.index }}
            </span>
        </template>
    </el-popover>
</template>

<script setup>
import { computed, useSlots } from 'vue';
import { useMarkdownRenderer } from '@/hooks/useMarkdown.js';
const slots = useSlots();
const md = useMarkdownRenderer();
const props = defineProps({
    title: {
        type: String,
        default: ''
    },
    content: {
        type: String,
        default: ''
    },
    contentKey: {
        type: String,
        default: ''
    }
});
const citeMeta = computed(() => {
    if (slots.default) {
        const nodes = slots.default() || [];
        const cite = nodes[0].children || '';
        const seg = cite.trim().split(/\s+/);
        let content = '';
        if (props.contentKey && window[props.contentKey]) {
            content = md.render(window[props.contentKey]).replace(/^<h1>(.+?)<\/h1>/g, '');
        }
        // if (nodes[1]) {
        //     content = nodes[1].children || '';
        // }
        // else if (seg[1]) {
        //     content = seg[1];
        // }
        return {
            index: seg[0].replace(/[\[\]]/g, ''),
            content: content || props.content
        };
    }
    return {
        index: '1',
        content: ''
    };
});
</script>

<style scoped lang="scss">
.llm-cite {
    cursor: pointer;
    white-space: nowrap;
    background: #dfdff566;
    display: inline-block;
    width: 14px;
    height: 14px;
    line-height: 14px;
    text-align: center;
    border-radius: 50%;
    vertical-align: text-bottom;
    font-size: 12px;
    margin: 0 2px;
    &:hover {
        background: #dfdff5;
    }
}
.llm-cite-content  {
    .header {
        color: #1b344a;
        font-size: 12px;
    }
    .text {
        .title {
            padding: 5px 0;
        }
        .content {
            font-size: 12px;
            color: #666;
            max-height: 300px;
            overflow-y: auto;
            :deep(p) {
                line-height: 1.4;
                margin-bottom: 5px;
            }
            :deep(h2) {
                font-size: 1.2em;
                padding-bottom: 0;
                border-bottom: none;
            }
        }
    }
}
</style>
