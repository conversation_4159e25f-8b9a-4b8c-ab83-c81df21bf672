<template>
    <div class="chat-message">
        <div class="message query">
            <!--<div class="message-content">{{ currentMessage.query }}</div>-->
            <div class="message-content">
                <div class="marked">
                    <markdown-renderer :content="currentMessage.query" />
                </div>
            </div>
        </div>
        <div class="message answer">
            <div class="avatar">
                <img src="@/assets/logo-white.png" width="40" alt="">
            </div>
            <div class="message-content">
                <!--<div class="marked" v-html="currentMessage.content"></div>-->
                <div class="think" v-if="currentMessage.think">
                    <md-think :status="currentMessage.status">
                        <div class="marked">
                            <markdown-renderer :content="currentMessage.think" />
                        </div>
                    </md-think>
                </div>
                <markdown-renderer class="marked" :content="currentMessage.content" />
                <template v-if="currentMessage.status===1 && currentMessage.content">
                    <div class="actions">
                        <div class="action">
                            <el-tooltip effect="dark" content="复制" placement="top">
                                <rp-icon name="icon-copy" @click="copyAnswer" />
                            </el-tooltip>
                        </div>
                        <div class="action">
                            <el-tooltip effect="dark" content="喜欢" placement="top">
                                <rp-icon
                                    name="icon-zan"
                                    @click="putMessageFeedback('like')"
                                    :color="currentMessage.feedback.rating==='like' ? 'var(--el-color-primary)' : '#333'"
                                />
                            </el-tooltip>
                        </div>
                        <div class="action">
                            <el-tooltip effect="dark" content="不喜欢" placement="top">
                                <rp-icon
                                    name="icon-cai"
                                    @click="putMessageFeedback('dislike')"
                                    :color="currentMessage.feedback.rating==='dislike' ? 'var(--el-color-error)' : '#333'"
                                />
                            </el-tooltip>
                        </div>
                    </div>
                    <div class="tips" style="border-top: 1px solid #ddd; margin-top: 5px; padding-top: 5px;">以上内容由AI大模型生成，仅供参考</div>
                </template>
            </div>
        </div>
    </div>
</template>

<script setup>
import { defineProps, ref, watchEffect, unref } from 'vue';
import { http } from '@/extensions/http';
import { httpApis } from '@/hooks/useHttpApi';
import 'katex/dist/katex.min.css';
import { ElMessageBox } from 'element-plus';
import { useToast } from '@/hooks/useToast';
import MarkdownRenderer from '@/components/markdown/MarkdownRenderer.vue';
import MdThink from '@/components/chat-widgets/think.vue';
import { copyText } from '@/utils/index';

const toast = useToast();
const currentMessage = ref(null);

const props = defineProps({
    message: {
        type: Object,
        required: true
    }
});
const putMessageFeedback = rating => {
    let ratingValue = rating;
    if (currentMessage.value.feedback.rating === rating) {
        ratingValue = null;
    }
    if (ratingValue !== 'dislike') {
        saveMessageFeedback({
            messageId: currentMessage.value.id,
            rating: ratingValue,
            content: ''
        });
        return;
    }
    ElMessageBox.prompt('请告诉我不对的地方，以便更好的进行改进', '意见反馈', {
        confirmButtonText: '提交反馈',
        cancelButtonText: '取消',
    }).then(({
        value
    }) => {
        saveMessageFeedback({
            messageId: currentMessage.value.id,
            rating: ratingValue,
            content: value
        });
    });
};
/**
 * 拷贝内容到剪切板
 */
const copyAnswer = () => {
    // 移除think过程
    const content = currentMessage.value.answer
        .replace(/<think[^>]*>[\s\S]*<\/think>/g, '')
        .replace(/<llm-think[^>]*>[\s\S]*<\/llm-think>/g, '');
    // if (navigator.clipboard) {
    //     navigator.clipboard.writeText(content).then(() => {
    //         toast.success('复制成功');
    //     }).catch(e => {
    //         console.log(e);
    //     });
    // }
    if (copyText(content)) {
        toast.success('复制成功');
    }
};
/**
 * 保存反馈信息
 * @param data
 */
const saveMessageFeedback = (data) => {
    http.post(httpApis.putMessageFeedback, data).then(() => {
        currentMessage.value.feedback.rating = data.rating;
    });
};

watchEffect(() => {
    const message = unref(props.message);
    let content = message.answer;
    // 拆分answer为think+content
    if (/^\s*<think>/.test(content)) {
        const idx = content.indexOf('</think>');
        const startIdx = content.indexOf('<think>') + 7;
        if (idx === -1) {
            message.think = content.slice(startIdx);
            content = '';
            message.status = message.status || 0;
        }
        else {
            message.think = content.slice(startIdx, idx);
            content = content.slice(idx + 8);
            message.status = 1;
        }
    }
    let knowledge = message.knowledge || {};
    if (Object.keys(knowledge).length === 0) {
        // 抓取参考文档
        knowledge = extractDocuments(content);
    }
    content = content
        .replace(/^\s+/g, '')
        // .replace(/<\/?\w{0,5}$/g, '') // 避免不完整的mark,think标签展示出来
        .replace(/<\/?([a-zA-Z]{1,4})[^>]*$/g, (a, b) => {
            if (b.indexOf('mark') === 0 || b.indexOf('cite') === 0) {
                return '';
            }
            return a;
        }) // 避免不完整的mark,think标签展示出来
        .replace(/^<details[^>]+>/g, '<llm-think class="md-think" status="' + message.status + '">')
        .replace(/<\/details>/g, '</llm-think>')
        .replace(/<summary>(.+?)<\/summary>/g, '')
        .replace(/^<think[^>]*>/g, '<llm-think class="md-think" status="' + message.status + '">')
        .replace(/<\/think>/g, '</llm-think>\n\n')
        .replace(/[(（]*\[(文档\s*\d+)\][）)]*/g, (v, key) => {
            key = key.replace(/\s+/g, '');
            if (knowledge[key]) {
                const idx = knowledge[key].index;
                const contentKey = `${message.id}-${idx}`;
                window[contentKey] = knowledge[key].content || '';
                return `<llm-cite title="${knowledge[key].title}" content-key="${contentKey}">[${idx}]</llm-cite>`;
            }
            return `[${key}]`;
        })
        .replace(/\[文档\s*(\d+)\]/g, (v, key) => {
            if (key) {
                return `[${key}]`;
            }
            return v;
        })
        .replace(/<(mark|think|cite)/g, '<llm-$1')
        .replace(/<\/(mark|think|cite)>/g, '</llm-$1>');
    message.content = content;
    currentMessage.value = message;
});


function extractDocuments(text) {
    const result = {};

    // 正则表达式匹配 [文档 数字] 或 [文档数字] 格式
    const docPattern = /\[文档\s*(\d+)\]\s*([^\n\r]+)/g;

    let match;
    while ((match = docPattern.exec(text)) !== null) {
        const docNumber = match[1];
        const docContent = match[2].trim();

        // 根据文档编号生成键名（保持原文格式）
        const key = `文档${docNumber}`;
        result[key] = {
            index: docNumber,
            title: docContent,
            content: ''
        };
    }

    return result;
}
</script>

<style scoped lang="scss">
.chat-message {
    font-size: 14px;
    margin: 0 20rpx;
    word-break: break-word;
    overflow-wrap: break-word;
    word-wrap: break-word;
    white-space: normal;
    .message {
        font-size: 16px;
        display: flex;
        padding: 10rpx 0;
        margin-bottom: 10px;
        .avatar {
            //width: 70rpx;
            //height: 70rpx;
            font-size: 14px;
            color: #555;
            margin-bottom: 4px;
            line-height: 25px;
            img {
                //border: 1px solid var(--el-color-primary);
                border-radius: 50%;
            }
        }
        .message-content {
            .footer {
                display: flex;
                align-items: center;
                margin-top: 5px;
            }
            .tips {
                flex: 1;
                font-size: 11px;
                color: #ff5c00;
            }
            .actions {
                .action-item {
                    margin: 0 10px;
                    &.active {
                        color: var(--el-color-primary);
                    }
                }
            }
        }
        &.query {
            justify-content: flex-end;
            .message-content {
                max-width: 80%;
                background-color: #e9f0fb; // var(--el-color-primary);
                //color: #fff;
                border-radius: 10px;
                padding: 8px;
            }
        }
        &.answer {
            //justify-content: flex-start;
            display: flex;
            background: #fff;
            padding: 8px 0;
            border-radius: 10px;
            .message-content {
                max-width: 100%;
                flex: 1;
                margin-left: 10px;
            }
        }
        .actions {
            display: flex;
            gap: 15px;
            align-items: center;
            .action {
                cursor: pointer;
            }
        }
    }
}
</style>
