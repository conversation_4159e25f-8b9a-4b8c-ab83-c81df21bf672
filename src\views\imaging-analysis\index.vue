<template>
    <el-container
        class="iar-index" :class="{
            'is-sidebar-collapsed': isSidebarCollapsed
        }"
    >
        <el-aside class="sidebar" width="260px">
            <sidebar :is-sidebar-collapsed="isSidebarCollapsed" @update:is-sidebar-collapsed="handleSidebarChange" />
        </el-aside>
        <el-main>
            <mobile-header :is-sidebar-collapsed="isSidebarCollapsed" @update:is-sidebar-collapsed="handleSidebarChange" />
            <router-view />
        </el-main>
    </el-container>
</template>

<script setup>
import Sidebar from './widgets/Sidebar.vue';
import MobileHeader from './widgets/MobileHeader.vue';
import { useLocalStorage } from '@vueuse/core';
const isSidebarCollapsed = useLocalStorage('imaging-analysis-sidebar', false);
const handleSidebarChange = (collapsed) => {
    isSidebarCollapsed.value = collapsed;
};
</script>

<style scoped lang="scss">
.iar-index {
    height: 100%;
    .sidebar {
        border-right: 1px solid var(--el-border-color);
    }
}
.is-sidebar-collapsed {
    .sidebar {
        width: 50px;
    }
}
.is-mobile {
    .is-sidebar-collapsed {
        .sidebar {
            display: none;
        }
    }
    .sidebar {
        width: 100%;
        position: relative;
        z-index: 101;
        border: none;
    }
}
</style>
