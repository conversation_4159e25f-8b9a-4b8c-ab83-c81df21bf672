<template>
    <div
        class="voice-text"
        ref="voiceTextRef"
        :style="{
            height: props.height
        }"
    >
        <div class="voice-text-content">{{ text }}</div>
    </div>
</template>

<script setup>
import { watch, ref, onMounted } from 'vue';
const voiceTextRef = ref(null);
const props = defineProps({
    text: {
        type: String,
        default: ''
    },
    height: {
        type: String,
        default: '2em'
    }
});
watch(() => props.text, (newVal) => {
    voiceTextRef.value.scrollTop = voiceTextRef.value.scrollHeight;
});
onMounted(() => {
    voiceTextRef.value.scrollTop = voiceTextRef.value.scrollHeight;
});
</script>

<style scoped lang="scss">
.voice-text {
    overflow: hidden;
    .voice-text-content {
        min-height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
</style>
