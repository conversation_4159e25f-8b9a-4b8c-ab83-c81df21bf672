<template>
    <div class="chat-messages" ref="messagesContainer">
        <div
            v-for="(message, index) in props.messages"
            :key="index"
        >
            <chat-message :message="message" />
        </div>
    </div>
</template>

<script setup>
import ChatMessage from './ChatMessage.vue';

const props = defineProps({
    messages: {
        type: Array,
        required: true
    }
});
</script>

<style scoped lang="scss">

</style>
