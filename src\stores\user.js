import { defineStore } from 'pinia';
import { useShowDialog } from '@/ui/index.js';
import LoginBox from '@/views/login/login-box.vue';
import { httpApis } from '@/hooks/useHttpApi';
import { http } from '@/extensions/http';
let checkLoginStatePromise = null;
export const useUserStore = defineStore('user', {
    state: () => ({
        user: null,
    }),
    getters: {
        isLogin: (state) => !!state.user,
    },
    actions: {
        login(user) {
            this.user = user;
        },
        getToken() {
            return this.user?.token;
        },
        getUser() {
            return this.user?.userInfo;
        },
        clearUser() {
            this.user = null;
        },
        async logout() {
            await http.post(httpApis.logout);
            this.clearUser();
        },
        checkLoginState(force = false) {
            if (checkLoginStatePromise && !force) {
                return checkLoginStatePromise;
            }
            const freeCheckLoginStatePromise = function () {
                setTimeout(() => {
                    checkLoginStatePromise = null;
                }, 200);
            };
            if (!this.isLogin) {
                checkLoginStatePromise = http.get(httpApis.getLoginInfo, {
                    auth: false
                }).then(res => {
                    this.login(res.data);
                    if (!this.isLogin) {
                        return useShowDialog(LoginBox, {}, {
                            title: '登录好兽医AI助手',
                            width: '460px'
                        }).then((user) => {
                            freeCheckLoginStatePromise();
                            return Promise.resolve(user);
                        }).catch(() => {
                            freeCheckLoginStatePromise();
                            return Promise.reject(new Error('取消登录'));
                        });
                    }
                    else {
                        return Promise.resolve(this.user);
                    }
                }).catch(e => {
                    return useShowDialog(LoginBox, {}, {
                        title: '登录好兽医AI助手',
                        width: '460px'
                    }).then((user) => {
                        freeCheckLoginStatePromise();
                        return Promise.resolve(user);
                    }).catch(() => {
                        freeCheckLoginStatePromise();
                        return Promise.reject(new Error('取消登录'));
                    });
                });
            }
            else {
                return Promise.resolve(this.user);
            }
            // checkLoginStatePromise = new Promise((resolve, reject) => {
            //     if (!this.isLogin) {
            //         return useShowDialog(LoginBox, {}, {
            //             title: '登录好兽医AI助手',
            //             width: '460px'
            //         }).then((user) => {
            //             freeCheckLoginStatePromise();
            //             resolve(user);
            //         }).catch(() => {
            //             freeCheckLoginStatePromise();
            //             reject(new Error('取消登录'));
            //         });
            //     }
            //     else {
            //         resolve(this.user);
            //     }
            // });
            return checkLoginStatePromise;
        },
    },
    persist: {
        storage: window.localStorage,
        pick: ['user']
    }
});
