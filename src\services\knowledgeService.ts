import { useCommonStore } from "@/stores/common";
import { fetchEventSource } from "@microsoft/fetch-event-source";
import type { ChatMessage } from "./chatService";

// 发送消息的请求参数
export interface SendMessageRequest {
  message: ChatMessage[];
}

// 流式响应的数据块
export interface BookInfo {
  id: string;
  name: string;
  content: string;
  pages: string;
}

// 流式响应的数据块
export interface StreamChunk {
  content: string;
  thinking: string;
  finished: boolean;
  thinkFinished: boolean;
  book?: BookInfo;
}

/**
 * 聊天服务类
 */
export class KnowledgeService {
  // 用于中止请求的控制器
  private static abortController: AbortController | null = null;
  // 重试计数器
  private static retryCount: number = 0;
  // 最大重试次数
  private static readonly MAX_RETRIES = 3;

  /**
   * 中止当前的流式请求
   */
  static abortCurrentRequest(): void {
    if (this.abortController) {
      this.abortController.abort();
      this.abortController = null;
    }
  }

  /**
   * 发送消息并获取流式响应
   * 这个方法用于实现打字机效果
   */
  static async sendMessageStream(
    params: SendMessageRequest,
    onChunk: (chunk: StreamChunk) => void,
    onChunkBook: (books: BookInfo[]) => void
  ): Promise<void> {
    const { message } = params;
    // 创建新的中止控制器
    this.abortController = new AbortController();
    // 重置重试计数器
    this.retryCount = 0;

    let totalContent = "";
    let totalBooks: BookInfo[] = [];
    let totalThinking = "";
    let bookIndex = 0;

    try {
      await fetchEventSource("knowledge-api/chat", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "text/event-stream",
        },
        signal: this.abortController.signal,
        body: JSON.stringify({
          messages: message
            .filter((item) => !!item.content)
            .map((item) => ({
              role: item.type === "user" ? "user" : "assistant",
              content: item.content,
            })),
          language: "zh"
        }),
        async onopen(response) {
          if (response.ok) {
            // 连接成功，重置重试计数器
            KnowledgeService.retryCount = 0;
            return; // 一切正常
          } else if (
            response.status >= 400 &&
            response.status < 500 &&
            response.status !== 429
          ) {
            // 客户端错误，不重试
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          } else {
            // 服务器错误或其他问题，可能会重试
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }
        },
        onmessage(event) {
          const jsonData = JSON.parse(event.data);
          // 检查是否是结束标记
          if (jsonData.finished) {
            onChunk({
              content: "",
              thinking: "",
              thinkFinished: true,
              finished: true,
            });
            return;
          }
          // 根据实际的 API 响应格式提取内容
          let content = "";

          if (jsonData.content) {
            content = jsonData.content;
          }

          if (event.event === "message" && content) {
            totalContent += content;
            if (content.includes("]")) {
              // 需要延迟替换，等待所有books数据加载完毕后再替换
              // 这里先做标记替换
              totalContent = totalContent.replace(
                /\[文档片段(\d+)\]/g,
                '<span class="document-book circled-number" data-doc-id="$1">$1</span>'
              );
            }
            // 发送增量更新
            onChunk({
              content: totalContent,
              thinking: totalThinking,
              thinkFinished: true,
              finished: false,
            });
          } else if (event.event === "chunk") {
            bookIndex += 1;
            const bookName = jsonData.doc_metadata.title as string;
            const blocks = jsonData.chunk_metadata.blocks
            let pages = ""
            content = jsonData.content;
            if (blocks && blocks.length > 0) {
              if (blocks.length === 1) {
                pages = `P${blocks[0].anchor.page}`
              } else {
                const startPage = blocks[0].anchor.page
                const lastPage = blocks[blocks.length - 1].anchor.page
                pages = startPage === lastPage ? `P${startPage}` : `P${startPage}~P${lastPage}`
              }

            }
            totalBooks = [
              ...totalBooks,
              {
                id: bookIndex + "",
                name: bookName,
                content: content,
                pages: pages
              },
            ];
            onChunkBook(totalBooks);
          }
        },
        onerror(error) {
          const httpCode: number = parseInt(
            error.message.split(":")[0].replace("HTTP", "").trim()
          );
          console.error(
            "SSE连接错误:",
            error,
            "重试次数:",
            KnowledgeService.retryCount
          );
          // 检查重试次数
          if (KnowledgeService.retryCount >= KnowledgeService.MAX_RETRIES) {
            console.error("已达到最大重试次数，停止重试");
            throw new Error("连接失败，请稍后重试");
          }

          if (httpCode >= 400 && httpCode < 500 && httpCode !== 429) {
            if (httpCode === 401) {
              //Pina库
              const commonStore = useCommonStore();
              commonStore.showLogin = true;
            }
            throw error;
          } else if (isNaN(httpCode)) {
            throw new Error("处理错误，请稍候重试");
          }
          KnowledgeService.retryCount++;
        },
        onclose() {
          // 在连接关闭时，进行最终的文档片段内容替换
          if (totalContent && totalBooks.length > 0) {
            // 替换所有文档片段标签，添加对应的内容
            totalContent = totalContent.replace(
              /<span class="document-book circled-number" data-doc-id="(\d+)">\1<\/span>/g,
              (_match, docId) => {
                const book = totalBooks.find(book => book.id === docId);
                const content = book ? book.content.replace(/"/g, '&quot;').replace(/'/g, '&apos;').replace(/\n/g, ' ') : '暂无内容';
                const name = book ? book.name.replace(/"/g, '&quot;').replace(/'/g, '&apos;') : '未知文档';
                const pages = book ? book.pages : '';
                return `<span class="document-book circled-number" data-doc-id="${docId}" data-content="${content}" data-name="${name}" data-pages="${pages}">${docId}</span>`;
              }
            );
          }

          // 连接关闭时确保发送最终完成状态
          if (totalContent || totalThinking) {
            onChunk({
              content: totalContent,
              thinking: totalThinking,
              finished: true,
              thinkFinished: true,
            });
          }
        },
      });
    } finally {
      // 清理中止控制器
      this.abortController = null;
    }
  }
}

// 默认导出
export default KnowledgeService;
