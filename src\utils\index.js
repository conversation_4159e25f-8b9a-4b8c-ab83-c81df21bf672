import { toRaw, isRef, isReactive, unref } from 'vue';
import dayjs from 'dayjs';

/**
 * 响应式对象转换为纯对象
 * @param obj
 * @return {*|*[]|{}}
 */
export function toPlainObject(obj) {
    if (obj === null || typeof obj !== 'object') return obj;
    let target = obj;
    if (isRef(obj)) {
        target = unref(obj);
    }
    else if (isReactive(obj)) {
        target = toRaw(obj);
    }
    const plain = Array.isArray(target) ? [] : {};
    for (const key in target) {
        const value = target[key];
        plain[key] = toPlainObject(value);
    }
    return plain;
}

/**
 * 手机号加密
 * @param phoneNumber
 * @return {*}
 */
export function maskPhoneNumber(phoneNumber) {
    if (!phoneNumber) {
        return phoneNumber;
    }
    return phoneNumber.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
}

/**
 * 时间友好化
 * @param time
 * @return {string}
 */
export function getTimeLabel(time) {
    const now = dayjs();
    const date = dayjs(time);
    const diffDays = now.diff(date, 'day');
    const diffYears = now.diff(date, 'year');
    if (diffDays < 1) {
        return '今天';
    }
    else if (diffDays < 7) {
        return '近7天';
    }
    else if (diffDays < 30) {
        return '近30天';
    }
    if (diffYears < 1) {
        return date.format('MM月');
    }
    return date.format('YYYY年');
}

/**
 * 拷贝文本到剪切板
 * @param text
 * @return {boolean}
 */
export function copyText(text) {
    if (!text) {
        return false;
    }
    const input = document.createElement('textarea');
    input.value = text;
    document.body.appendChild(input);
    input.select();
    input.setSelectionRange(0, 99999); // 移动端兼容
    const result = document.execCommand('copy');
    input.blur();
    document.body.removeChild(input);
    return result;
}

/**
 * cos缩略图
 * @param imageUrl
 * @param width
 * @param height
 * @return {string}
 */
export function thumbnail(imageUrl, width, height) {
    return `${imageUrl}?imageMogr2/crop/${width}x${height}/gravity/center`;
}

/**
 * 将选择的图片转为base64编码
 * @param file
 * @return {Promise<unknown>}
 */
export function file2base64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => {
            // 将result拆分为minetype和base64编码
            const base64 = reader.result.split(',')[1];
            const mineType = reader.result.split(',')[0].split(':')[1].split(';')[0];
            resolve({
                base64,
                mineType,
                result: reader.result
            });
        };
        reader.onerror = () => {
            reject(new Error('文件读取失败'));
        };
    });
}
// 根据modelValue解析出年龄
export const parseAgeFromDate = (dateStr) => {
    if (!dateStr) {
        return {
            ageYear: '',
            ageMonth: ''
        };
    }
    const birthDate = dayjs(dateStr);
    const now = dayjs();
    if (!birthDate.isValid()) return;

    const yearDiff = now.diff(birthDate, 'year');
    const monthDiff = now.subtract(yearDiff, 'year').diff(birthDate, 'month');

    return {
        ageYear: yearDiff.toString(),
        ageMonth: monthDiff.toString()
    };
};
/**
 * 判断元素是否可滚动
 * @param {Element} el
 * @returns {boolean}
 */
function isScrollable(el, strict = true) {
    const style = getComputedStyle(el);
    const overflowY = style.overflowY;
    const isScrollableY = overflowY === 'auto' || overflowY === 'scroll';
    if (strict) {
        return isScrollableY && el.scrollHeight > el.clientHeight;
    }
    return isScrollableY;
}

/**
 * 查找可滚动的父级元素
 * @param {Element} el
 * @returns {Element | null}
 */
export function findScrollableParent(el, strict = true) {
    let parent = el?.parentElement;
    while (parent) {
        if (isScrollable(parent, strict)) {
            return parent;
        }
        parent = parent.parentElement;
    }
    return window.document.documentElement;
}
