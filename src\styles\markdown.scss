.marked {
  // 标题样式
  h1, h2, h3, h4, h5, h6 {
    margin-top: 20px;
    margin-bottom: 10px;
    font-weight: 600;
    line-height: 1.25;
    &:first-child {
      margin-top: 0;
    }
  }

  h1 {
    font-size: 2em;
    padding-bottom: 0.3em;
    border-bottom: 1px solid #eaecef;
  }

  h2 {
    font-size: 1.5em;
    padding-bottom: 0.3em;
    border-bottom: 1px solid #eaecef;
  }

  h3 {
    font-size: 1.25em;
  }

  h4 {
    font-size: 1em;
  }

  h5 {
    font-size: 0.875em;
  }

  h6 {
    font-size: 0.85em;
    color: #6a737d;
  }

  // 段落样式
  p {
    margin-top: 0;
    margin-bottom: 16px;
    line-height: 1.6;
    &:last-child {
      margin-bottom: 0;
    }
  }

  // 列表样式
  ul, ol {
    padding-left: 2em;
    margin-top: 0;
    margin-bottom: 16px;
    ul, ol {
      margin-bottom: 0;
    }
    &:last-child {
      margin-bottom: 0;
    }
  }

  ul {
    list-style-type: disc;
  }

  ol {
    list-style-type: decimal;
  }

  li {
    margin-top: 0.25em;
    > p:first-child {
      margin-bottom: 0;
    }
  }

  // 代码块样式
  pre {
    padding: 16px;
    overflow: auto;
    font-size: 85%;
    line-height: 1.45;
    background-color: #f6f8fa;
    border-radius: 6px;
    margin-top: 0;
    margin-bottom: 16px;
  }

  code {
    font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace;
    font-size: 85%;
    padding: 0.2em 0.4em;
    margin: 0;
    background-color: rgba(27, 31, 35, 0.05);
    border-radius: 3px;
  }

  pre code {
    padding: 0;
    margin: 0;
    background-color: transparent;
    border: 0;
    color: #333;
  }

  // 引用样式
  blockquote {
    padding: 0 1em;
    color: #6a737d;
    border-left: 0.25em solid #dfe2e5;
    margin: 0 0 16px 0;
  }

  // 表格样式
  table {
    width: 100%;
    max-width: 100%;
    overflow: auto;
    margin-top: 0;
    margin-bottom: 16px;
    border-spacing: 0;
    border-collapse: collapse;
    color: #333;
  }

  table th {
    font-weight: 600;
  }

  table th,
  table td {
    padding: 6px 13px;
    border: 1px solid #dfe2e5;
  }

  table tr {
    background-color: #fff;
    border-top: 1px solid #c6cbd1;
  }

  table th {
    background: #eef3f6;
  }

  table tr:nth-child(2n) {
    background-color: #f6f8fa;
  }

  // 链接样式
  a {
    color: #0366d6;
    text-decoration: none;
  }

  a:hover {
    text-decoration: underline;
  }

  // 图片样式
  img {
    max-width: 100%;
    box-sizing: content-box;
    background-color: #fff;
  }

  // 分割线样式
  hr {
    height: 2px;
    padding: 0;
    margin: 20px 0;
    background-color: #e1e4e8;
    border: 0;
  }

  // 强调样式
  strong {
    font-weight: 600;
  }

  em {
    font-style: italic;
  }

  // 删除线样式
  del {
    text-decoration: line-through;
  }
  .katex-html {
    //display: none;
  }
  // mermaid plugin
  .edgeLabel {
    font-size: 14px !important;
  }
  .rag-result {
    font-size: 12px;
    border: 1px solid #eeeeee;
    padding: 10px;
    border-radius: 5px;
    background: #f6f6f6;
  }
}
