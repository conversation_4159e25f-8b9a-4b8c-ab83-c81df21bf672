<template>
    <div class="clipboard" @click.stop.prevent="copyToClipboard">
        <slot>
            <!--<el-tooltip effect="dark" content="复制" placement="top" :show-after="200"></el-tooltip>-->
            <span class="text">
                <rp-icon name="icon-copy" size="12" />复制
            </span>
        </slot>
    </div>
</template>

<script setup>
import { copyText } from '@/utils/index.js';
import { useToast } from '@/hooks/useToast.js';
defineOptions({
    name: 'RpClipboard'
});
const props = defineProps({
    text: {
        type: String,
        required: true,
    },
    disabled: {
        type: Boolean,
        default: false,
    }
});

// 复制到剪贴板
const copyToClipboard = () => {
    if (props.disabled) {
        return;
    }
    copyText(props.text);
    useToast({
        message: '复制成功',
        type: 'success'
    });
};
</script>

<style scoped lang="scss">
.clipboard {
    display: inline-block;
    cursor: pointer;
    color: var(--el-color-primary);
    .text {
        font-size: 12px;
    }
}
</style>
