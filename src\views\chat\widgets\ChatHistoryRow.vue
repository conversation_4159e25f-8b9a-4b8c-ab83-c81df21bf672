<template>
    <div class="chat-history-row" :class="{ 'active': isActive }">
        <el-tooltip content="知识库" placement="top" v-if="row.inputs && row.inputs.scene && row.inputs.scene==='knowledge'">
            <rp-icon name="document" style="vertical-align: -1px" />
        </el-tooltip>
        {{ row.name }}
    </div>
</template>

<script setup>
import { computed } from 'vue';
import { useRoute } from 'vue-router';
const route = useRoute();
const props = defineProps({
    row: {
        type: Object,
        required: true
    }
});
const isActive = computed(() => {
    return route.params.id === props.row.id;
});
</script>

<style scoped lang="scss">
.chat-history-row {
    font-size: 14px;
    padding: 8px;
    //margin: 0 8px;
    border-radius: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    &:hover {
        background-color: #e1effd;
        cursor: pointer;
    }
    &.active {
        background-color: #cce6ff;
    }
}
</style>
