<template>
    <div class="chat">
        <el-container>
            <chat-sidebar />

            <el-main class="chat-main">
                <div class="chat-title">标题</div>
                <div
                    ref="scrollRef"
                    class="chat-container" :class="{
                        'chat-container-empty': currentChat.messages.length === 0
                    }"
                >
                    <div class="chat-container-inner">
                        <!--<chat-messages class="messages" :messages="currentChat.messages" />-->
                        <div class="messages" ref="messagesRef"></div>
                        <div class="bottom-gap"></div>
                        <chat-input class="inputbox" @send-message="handleSendMessage">
                            <div class="empty-state" v-if="currentChat.messages.length === 0">
                                <div>
                                    <img src="@/assets/logo.png" alt="">
                                </div>
                                <h2>我是好兽医AI助手，今天我能帮你做什么？</h2>
                                <div class="placeholder-text">有什么问题可以随时问我~</div>
                            </div>
                            <template #bottom>
                                <div class="chat-footer">
                                    内容由AI生成，请仔细甄别
                                </div>
                            </template>
                        </chat-input>
                    </div>
                </div>
            </el-main>
        </el-container>
    </div>
</template>
<script setup>
import { reactive, ref, toRef, createApp, onMounted, nextTick } from 'vue';
import ChatSidebar from './widgets/ChatSidebar.vue';
import ChatMessages from './widgets/ChatMessages.vue';
import ChatInput from './widgets/ChatInput.vue';
import ui from '@/ui/index';
import { http } from '@/extensions/http';
import { httpApis } from '@/hooks/useHttpApi';
import { useUserStore } from '@/stores/user.js';
import think from '@/components/chat-widgets/think.vue';
import mark from '@/components/chat-widgets/mark.vue';
import llmCite from '@/components/chat-widgets/cite.vue';
import MarkdownContainer from '@/components/markdown/MarkdownContainer.vue';
const userStore = useUserStore();
const chatScene = ref('');
const messagesRef = ref(null);
const scrollRef = ref(null);
const isChatMessageRunning = ref(false);
const currentChat = reactive({
    messages: []
});

onMounted(() => {
    const app = createApp(ChatMessages, {
        messages: currentChat.messages
    });
    app.component('LlmThink', think);
    app.component('LlmMark', mark);
    app.component('LlmCite', llmCite);
    app.component('MarkdownContainer', MarkdownContainer);
    app.use(ui);
    app.mount(messagesRef.value);
    nextTick(() => {
        scrollToBottom();
    });
});

// currentChat.messages.push(toRef({
//     inputs: {},
//     query: '你好！有什么我可以帮忙的吗？',
//     nodeEvent: [],
//     feedback: {
//         rating: null
//     },
//     status: 1,
//     answer: `
// <think>
// xxxlhello
// </think>
//
// \`\`\`
// var a=1;
// \`\`\`
//
// - **典型表现**：丘疹、脓疱、表皮环状脱屑、瘙痒，金毛为易感品种
//
// #### 4. 病理分期依据
// IRIS肾病分期标准：
//
// | 临床指标 | 1期 | 2期 | 3期 | 4期 |
// |----------|------|------|------|------|
// | BUN(mg/dL) | <25 | 25-40 | 41-60 | >60 |
// | Cr(μmol/L) | <140 | 140-200 | 201-350 | >350 |
// | 尿比重 | >1.035 | 1.015-1.035 | <1.015 | - |
// | 贫血 | - | - | HCT<35% | HCT<25% |
// `
// }));
// currentChat.messages.push(toRef({
//     inputs: {},
//     query: '你好！有什么我可以帮忙的吗？',
//     nodeEvent: [],
//     feedback: {
//         rating: null
//     },
//     status: 1,
//     answer: `
// ## LaTex
//
// $x = \\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}$
//
// // Euler\\'s identity $e^{i\\\\pi}+1=0$ is a beautiful formula in $RR^2$. <mark type="inspection">Hello</mark>
//
// 疾病介绍<mark type="sick">oooo</mark> 结束了
//
// 这是一个超链接[baidu](https://baidu.com) -- 二号楼
//
// <span class="llm-sick">肠胃炎</span>
// test
//         `
// }));

const handleNewChat = () => {
    currentChat.messages = [];
};

const scrollToBottom = () => {
    if (scrollRef.value) {
        scrollRef.value.scrollTop = scrollRef.value.scrollHeight;
    }
};

const handleSendMessage = (inputMessage) => {
    if (!inputMessage.trim()) {
        return;
    }
    const inputs = {};
    if (chatScene.value) {
        inputs.scene = chatScene.value;
    }
    const conversationId = '';
    const message = toRef({
        inputs: {},
        query: inputMessage,
        answer: '',
        nodeEvent: [],
        feedback: {
            rating: null
        },
        status: 0,
        conversation_id: conversationId,
        taskId: null,
        created_at: parseInt(Date.now() / 1000, 10)
    });
    currentChat.messages.push(message);
    const answers = [];
    function handleMessageTrunk(trunk) {
        message.value.taskId = trunk.task_id;
        switch (trunk.event) {
        case 'queue':
        case 'message_replace':
            answers.splice(0);
            answers.push(trunk.answer);
            break;
        case 'message':
            answers.push(trunk.answer);
            break;
        case 'error':
            message.value.status = 1;
            answers.push(`<div style="color:red;">${trunk.message}</div>`);
            break;
        case 'node_started':
            message.value.nodeEvent.push({
                status: 'running',
                start: trunk.data,
                end: null
            });
            break;
        case 'node_finished':
            message.value.nodeEvent.forEach(item => {
                if (item.start.id === trunk.data.id) {
                    item.end = trunk.end;
                    item.status = 'finished';
                }
            });
            break;
        case 'message_end':
            message.value.status = 1;
            isChatMessageRunning.value = false;
            break;
        }
        message.value.answer = answers.join('');
        nextTick(() => {
            scrollToBottom();
        });
    }
    let isReady = false;
    http.post(httpApis.sendChatMessage, {
        inputs,
        query: inputMessage,
        response_mode: 'streaming',
        conversation_id: conversationId,
        user: userStore.getUser().id,
        files: []
    }, {
        responseType: 'stream'
    }).then(async res => {
        const reader = res.data.getReader();
        // 将流中的字节数据解码为文本字符串
        const textDecoder = new TextDecoder();
        let result = true;
        while (result) {
            // done表示流是否已经完成读取  value包含读取到的数据块
            const {
                done, value
            } = await reader.read();
            if (done) {
                result = false;
                break;
            }
            // 拿到的value就是后端分段返回的数据，大多是以data:开头的字符串
            // 需要通过decode方法处理数据块，例如转换为文本或进行其他操作
            textDecoder
                .decode(value)
                .split('\n\n')
                .forEach((val) => {
                    if (!val) return;
                    try {
                        // 后端返回的流式数据一般都是以data:开头的字符，排除掉data:后就是需要的数据
                        // 具体返回结构可以跟后端约定
                        let text = val?.replace('data:', '') || '';
                        if (text) {
                            try {
                                const trunk = JSON.parse(text);
                                if (!isReady) {
                                    message.value.id = trunk.message_id;
                                    message.value.conversation_id = trunk.conversation_id;
                                    message.value.answer = '';
                                }
                                handleMessageTrunk(trunk);
                            }
                            catch (e) {
                                console.log('解析失败：', text);
                                console.error(e);
                            }
                        }
                    }
                    catch (err) {
                        console.error(err);
                    }
                });
        }
    }).catch(e => {
        message.value.status = 1;
        message.value.answer = '抱歉，服务出现异常，请稍后重试';
    }).finally(() => {
        isChatMessageRunning.value = false;
    });
};
</script>

<style lang="scss" scoped>
.chat {
    height: 100vh;

    .el-container {
        height: 100%;
    }
}

.chat-main {
    padding: 0;
    position: relative;
    .chat-title {
        text-align: center;
        height: 50px;
    }
    .chat-container {
        height: calc(100vh - 50px);
        overflow-y: auto;
        .bottom-gap {
            height: 140px;
        }
        &-empty {
            justify-content: center;
            .messages {
            }
            .messages, .chat-footer {
                display: none;
            }
            .inputbox {
                position: static;
            }
            .bottom-gap {
                height: 0;
            }
        }
        &-inner {
            position: relative;
            width: 670px;
            margin: 0 auto;
        }
    }
    .inputbox {
        position: fixed;
        bottom: 0;
        width: 670px;
        background: #fff;
        padding: 0 20px;
    }
    .chat-footer {
        text-align: center;
        font-size: 13px;
        color: #999;
        height: 30px;
        width: 100%;
        line-height: 30px;
    }
    .empty-state {
        text-align: center;
        margin-bottom: 40px;
    }
}
</style>
