<template>
    <div class="iar-history">
        <div class="title">历史记录</div>
        <div
            class="list"
            style="height: calc(100% - 40px); overflow-y: auto"
            v-infinite-scroll="nextPage"
            :infinite-scroll-disabled="loading || !hasMore"
        >
            <div
                class="item" v-for="(record, index) in records" :key="index"
                :class="{active: +route.params.id===record.id}"
                @click="goMedicalDetail(record.id)"
            >
                <div class="item-header">
                    <div class="serials">#{{ record.id }}</div>
                    <div class="item-date">{{ record.createdAt }}</div>
                </div>
                <div class="item-content">
                    <div class="item-title-text">{{ record.name || record.result || '暂无名称' }}</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { onMounted, onUnmounted, ref } from 'vue';
import { http } from '@/extensions/http';
import { httpApis } from '@/hooks/useHttpApi';
import { useRouter } from 'vue-router';
import { useRoute } from 'vue-router';
import { useEventBus } from '@vueuse/core';
const route = useRoute();
const bus = useEventBus('imagingAnalysisBus');
const emit = defineEmits(['change']);
const router = useRouter();
const records = ref([]);
const hasMore = ref(true);
const loading = ref(false);
const current = ref(1);

bus.on((event) => {
    if (event === 'create') {
        current.value = 1;
        hasMore.value = true;
        records.value = [];
        loadHistory();
    }
});

onMounted(() => {
    loadHistory();
});
onUnmounted(() => {
    bus.off();
});
const loadHistory = () => {
    loading.value = true;
    return http.get(httpApis.listMyImagingAnalysisReports, {
        params: {
            current: current.value,
            size: 8
        }
    }).then(res => {
        console.log(res.data, res);
        records.value.push(...res.data);
        const {
            total, size, current
        } = res.originData;
        hasMore.value = total > size * current;
    }).catch(err => {
        console.log(err);
        hasMore.value = false;
    }).finally(() => {
        loading.value = false;
    });
};
const nextPage = () => {
    console.log('next page');
    if (loading.value || !hasMore.value) {
        return;
    }
    current.value++;
    loadHistory();
};
const goMedicalDetail = (id) => {
    router.push({
        name: 'ImagingAnalysisDetail',
        params: {
            id: id
        }
    });
    emit('change', id);
};
</script>

<style scoped lang="scss">
.iar-history {
    flex: 1;
    overflow: hidden;
    .title {
        padding: 8px 16px;
        height: 40px;
    }
    .list {
        height: 100%;
        overflow: auto;
        padding: 0 8px;
    }
    .item {
        padding: 4px 8px;
        border-radius: 5px;
        cursor: pointer;
        &:hover {
            background: #e1effd;
        }
        &.active {
            background-color: #cce6ff;
        }
    }
    .item-header {
        display: flex;
        align-items: center;
        .serials {
            flex: 1;
        }
        .item-date {
            font-size: 12px;
        }
    }
    .item-content {
        .item-title-text {
            font-size: 12px;
            color: #999;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2; /* 限制最大显示2行 */
            overflow: hidden;
        }
    }
}
</style>
