<template>
    <div ref="messagesRef"></div>
</template>

<script setup>
import { createApp, nextTick, onMounted, onUnmounted, reactive, ref, toRef, watch, watchEffect } from 'vue';
import { http } from '@/extensions/http.js';
import ChatMessages from './ChatMessages.vue';
import think from '@/components/chat-widgets/think.vue';
import mark from '@/components/chat-widgets/mark.vue';
import llmCite from '@/components/chat-widgets/cite.vue';
import ElementPlus from 'element-plus';
import ui from '@/ui/index.js';
import { findScrollableParent } from '@/utils/index.js';
import MarkdownContainer from '@/components/markdown/MarkdownContainer.vue';
const messagesRef = ref(null);
const scrollableEl = ref(null);
const cancelToken = ref(null);
const isLoading = ref(false);
// 是否开启自动滚动到底部
const isScrollEnabled = ref(true);
const chat = reactive({
    messages: [],
    id: ''
});
const props = defineProps({
    baseUrl: {
        type: String,
        required: true
    },
    inputs: {
        type: Object,
        default() {
            return {};
        }
    },
    thinking: {
        type: Boolean,
        default: true
    },
    messages: {
        type: Array,
        default() {
            return [];
        }
    }
});
const emit = defineEmits(['message', 'stopMessage']);
onMounted(() => {
    chat.messages = props.messages;
    const app = createApp(ChatMessages, {
        messages: chat.messages
    });
    app.component('LlmThink', think);
    app.component('LlmMark', mark);
    app.component('LlmCite', llmCite);
    app.component('MarkdownContainer', MarkdownContainer);
    app.use(ElementPlus);
    app.use(ui);
    app.mount(messagesRef.value);

    scrollableEl.value = findScrollableParent(messagesRef.value, false);
    scrollableEl.value.addEventListener('scroll', handleScrollElScroll);
});
onUnmounted(() => {
    if (scrollableEl.value) {
        scrollableEl.value.removeEventListener('scroll', handleScrollElScroll);
    }
});
watch(props.messages, () => {
    chat.messages.splice(0, chat.messages.length, ...props.messages);
    nextTick(() => {
        console.log('watch scroll btm');
        scrollToBottom(true);
    });
}, {
    deep: false
});
const handleScrollElScroll = () => {
    const el = scrollableEl.value;
    isScrollEnabled.value = el.scrollTop + el.clientHeight >= el.scrollHeight - 100;
};
const scrollToBottom = (force = false) => {
    const el = scrollableEl.value;
    if (el && (force || isScrollEnabled.value)) {
        el.scrollTop = el.scrollHeight;
    }
};
/**
 * 发送消息到服务端
 * @param inputQuery  输入的查询
 * @param inputs  输入的参数
 */
const sendMessage = (inputQuery, inputs = {}) => {
    console.log('sendMessage', inputQuery);
    const sendInputs = Object.assign({}, props.inputs, inputs);
    const textDecoder = new TextDecoder('utf-8');
    cancelToken.value = http.CancelToken.source();
    isLoading.value = true;
    const message = toRef({
        query: inputQuery.trim(),
        inputs: sendInputs,
        answer: '<span><rp-icon name="loading" class="is-loading" />思考中</span>',
        status: 0,
        messageId: '',
        rating: null,
        content: ''
    });
    chat.messages.push(message);
    const answer = [];
    function handleMessageTrunk() {
        const tail = message.value.status === 0 ? '<rp-icon name="loading" class="is-loading" />' : '';
        message.value.answer = answer.join('') + tail;
    }
    return http.post(props.baseUrl, {
        query: inputQuery.trim(),
        inputs: sendInputs
    }, {
        cancelToken: cancelToken.value.token,
        responseType: 'stream',
        timeout: 0,
        local: true
    }).then(async res => {
        const reader = res.data.getReader();
        let result = true;
        let buffer = '';
        if (props.thinking) {
            answer.push('<think>');
        }
        while (result) {
            // done表示流是否已经完成读取  value包含读取到的数据块
            const {
                done, value
            } = await reader.read();
            if (done) {
                result = false;
                break;
            }
            const chunk = textDecoder.decode(value, {
                stream: true
            });
            buffer += chunk;
            // 按照 event-stream 拆分数据块（以 \n\n 分隔）
            const parts = buffer.split('\n\n');
            buffer = parts.pop(); // 最后一段可能不完整，保留继续处理

            for (const part of parts) {
                const text = part.replace(/^data:/g, '')
                    .replace(/\ndata:/g, '\n');
                const json = JSON.parse(text);
                if (json.event === 'message') {
                    // answer.push(text);
                    if (!message.value.messageId) {
                        message.value.messageId = json.messageId;
                    }
                    answer.push(json.content);
                }
                else if (json.event === 'error') {
                    message.value.answer = `<div style="color: red;">${json.content}</div>`;
                    message.value.status = -1;
                    return;
                }
            }
            handleMessageTrunk();
            await nextTick(() => {
                scrollToBottom();
            });
        }
        message.value.status = 1;
    }).catch(e => {
        message.value.status = -1;
        if (e.name === 'AbortError' || e.name === 'CanceledError') {/* empty */ }
        else {
            console.error(e);
            message.value.answer = `<div style="color: red;">${e.message}</div>`;
        }
    }).finally(() => {
        handleMessageTrunk();
        isLoading.value = false;
    });
};
/**
 * 停止生成
 */
const stopGenerating = () => {
    if (cancelToken.value) {
        cancelToken.value.cancel('取消请求');
    }
};

defineExpose({
    sendMessage,
    stopGenerating
});
</script>

<style scoped lang="scss">

</style>
