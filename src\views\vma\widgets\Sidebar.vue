<template>
    <div class="vma-aside">
        <div class="aside-header">
            <div class="logo-box" @click="router.push({path: '/'})">
                <img src="@/assets/logo-white.png" class="logo" alt="logo">
                <h1>好兽医AI助手</h1>
            </div>
            <div class="collapse">
                <el-tooltip effect="dark" content="收起边栏" placement="right" :disabled="env.isMobile.value">
                    <rp-icon name="icon-sidebar-left" @click="closeSidebar" />
                </el-tooltip>
            </div>
        </div>
        <div class="open-collapse">
            <el-tooltip effect="dark" content="打开边栏" placement="right">
                <rp-icon name="icon-sidebar-right" @click="openSidebar" />
            </el-tooltip>
        </div>
        <div class="new-chat-btn">
            <el-button class="btn-has-text" type="primary" @click="openNewChat" round block>
                <rp-icon name="plus" size="16" />
                <span style="margin-left: 5px;">开启新病例</span>
            </el-button>
            <el-tooltip effect="dark" content="开启新病例" placement="right">
                <rp-icon class="btn-simple" name="plus" @click="openNewChat" />
            </el-tooltip>
        </div>
        <global-navigator-pc :is-sidebar-collapsed="isSidebarCollapsed" />
        <voice-medical-record-history @change="handleHistoryChange" />
        <user-center v-if="userStore.isLogin" />
    </div>
</template>

<script setup>
import { useRouter } from 'vue-router';
import UserCenter from '../../chat/widgets/UserCenter.vue';
import VoiceMedicalRecordHistory from '@/views/vma/widgets/VoiceMedicalRecordHistory.vue';
import { useUserStore } from '@/stores/user';
import { useEnv } from '@/hooks/useEnv';
import { onMounted } from 'vue';
import GlobalNavigatorPc from '@/components/common/GlobalNavigatorPc.vue';
const env = useEnv();
const userStore = useUserStore();

const router = useRouter();
const props = defineProps({
    isSidebarCollapsed: {
        type: Boolean,
        default: false
    }
});
const emit = defineEmits(['update:isSidebarCollapsed']);
onMounted(() => {
    if (env.isMobile.value) {
        closeSidebar();
    }
});
const openSidebar = () => {
    emit('update:isSidebarCollapsed', false);
};
const closeSidebar = () => {
    emit('update:isSidebarCollapsed', true);
};
const openNewChat = () => {
    router.push({
        name: 'VmaCreate',
        query: {
            t: Date.now()
        }
    });
};
const handleHistoryChange = (id) => {
    if (env.isMobile.value) {
        closeSidebar();
    }
};
</script>

<style lang="scss" scoped>
.vma-aside {
    background: #f5f7fa;
    transition: width 0.3s;
    display: flex;
    flex-direction: column;
    height: 100%;

    .aside-header {
        height: 50px;
        display: flex;
        padding: 0 8px;
        align-items: center;
        .logo-box {
            cursor: pointer;
            display: flex;
            align-items: center;
            flex: 1;
            color: #0068b7;
            .logo {
                height: 32px;
                border-radius: 5px;
            }
            h1 {
                flex: 1;
                white-space: nowrap;
                margin-left: 5px;
            }
        }
        .collapse {
            width: 30px;
            cursor: pointer;
            text-align: right;
        }
    }
    .open-collapse {
        display: none;
    }

    .new-chat-btn {
        padding: 10px 16px;
        .btn-simple {
            display: none;
        }
    }
}
.is-sidebar-collapsed {
    .aside-header {
        .collapse {
            display: none;
        }
    }
    .open-collapse {
        display: block !important;
        padding: 10px 16px;
        cursor: pointer;
    }
    .logo-box {
        h1 {
            display: none;
        }
    }
    .new-chat-btn {
        .btn-simple {
            display: block;
            cursor: pointer;
        }
        .btn-has-text {
            display: none;
        }
    }
    .user-center {
        :deep(.uc span) {
            display: none;
        }
    }
    .vma-history {
        display: none;
        :deep(.item-content) {
            display: none;
        }
    }
    .iconfont {
        font-size: 18px;
    }
}
.is-mobile {
    .new-chat-btn {
        display: none;
    }
}
</style>
