import { createRouter, createWebHistory } from 'vue-router';

const routes = [
    {
        path: '/',
        name: 'Home',
        component: () => import('../views/Home.vue')
    },
    {
        path: '/chat',
        name: 'Chat',
        component: () => import('../views/chat/index.vue')
    },
    {
        path: '/chat/new',
        name: 'ChatN<PERSON>',
        component: () => import('../views/chat/chat.vue'),
    },
    {
        path: '/chat/new/:id',
        name: 'ChatDetail',
        component: () => import('../views/chat/chat.vue'),
    },
    {
        path: '/vma',
        name: 'VMA',
        component: () => import('../views/vma/index.vue'),
        children: [
            {
                path: 'create',
                name: 'VmaCreate',
                component: () => import('../views/vma/create.vue')
            },
            {
                path: 'm/:id',
                name: 'VmaDetail',
                props: true,
                component: () => import('../views/vma/detail.vue')
            }
        ]
    },
    {
        path: '/imaging-analysis',
        name: 'ImagingAnalysis',
        component: () => import('../views/imaging-analysis/index.vue'),
        children: [
            {
                path: 'record',
                name: 'ImagingAnalysisCreate',
                component: () => import('../views/imaging-analysis/detail.vue')
            },
            {
                path: 'record/:id',
                name: 'ImagingAnalysisDetail',
                props: true,
                component: () => import('../views/imaging-analysis/detail.vue')
            },
        ]
    },
    {
        path: '/privacy',
        component: () => import('../views/misc/privacy.vue')
    },
    {
        path: '/agreement',
        component: () => import('../views/misc/agreement.vue')
    }
];

const router = createRouter({
    history: createWebHistory(),
    routes
});

export default router;
