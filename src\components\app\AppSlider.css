/* 侧边栏容器 */
.slider {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 18dvw;
  border-right: 1px solid rgba(200, 200, 200, 0.2);
  color: #333;
  box-shadow: inset -1px 0 0 rgba(200, 200, 200, 0.1);
}

.slider.collapsed {
  width: 4dvw;
  min-width: 4dvw;
}

.slider-logo {
  width: 100%;
}

.slider-logo-img {
  width: 60%;
  cursor: pointer;
}

.slider-logo-img-collapsed {
  width: 100%;
  cursor: pointer;
}

/* 标题样式 */
.slider h3 {
  margin-block: 0 0.6dvw;
  font-size: 0.9dvw;
}

/* 功能模块区域 */
.slider-function {
  flex: 0 0 auto;
  padding: 1.2dvw 1dvw;
}

.slider.collapsed .slider-function {
  padding: 1.2dvw 0.5dvw;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 0.6dvw;
}

.new-chat-icon {
  font-size: 0.8dvw;
  cursor: pointer;
  padding: 0.3dvw;
  border-radius: 0.4dvw;
  transition: all 0.2s ease;
  margin-right: 0.5dvw;
}

.new-chat-icon:hover {
  background: linear-gradient(135deg,
      rgba(200, 200, 200, 0.2) 0%,
      rgba(160, 160, 160, 0.15) 100%);
  box-shadow: 0 2px 8px rgba(160, 160, 160, 0.2);
}

/* 功能列表 */
.function-list {
  list-style: none;
  padding-left: 0;
  margin: 0;
}

.function-list li {
  padding: 0.4dvw;
  font-size: 0.8dvw;
  line-height: 1dvw;
  margin-bottom: 0.5dvw;
  display: flex;
  align-items: center;
  gap: 0.6dvw;
  border-radius: 0.4dvw;
  transition: all 0.2s ease;
  cursor: pointer;
}

.slider.collapsed .function-list li {
  justify-content: center;
  padding: 0.8dvw 0.5dvw;
  gap: 0;
}

.function-list li:last-child {
  margin-bottom: 0;
}

.function-list li:hover {
  background: linear-gradient(135deg,
      rgba(200, 200, 200, 0.2) 0%,
      rgba(160, 160, 160, 0.25) 100%);
  box-shadow: 0 2px 8px rgba(160, 160, 160, 0.2);
}

.function-list li.active {
  background: linear-gradient(135deg,
      rgba(200, 200, 200, 0.3) 0%,
      rgba(160, 160, 160, 0.35) 100%);
  box-shadow: 0 2px 12px rgba(160, 160, 160, 0.3);
}

.function-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.function-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 历史对话区域 */
.slider-history {
  flex: 1 1 0;
  display: flex;
  flex-direction: column;
  min-height: 0;
  padding: 0 0.1dvw 0 1dvw;
}

.history-list {
  flex: 1 1 0;
  min-height: 0;
  list-style: none;
  padding-left: 0;
  margin: 0;
  overflow-y: auto;
  padding-right: 0.5dvw;
}

/* 加载图标容器 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.5dvw 0 1dvw;
  font-size: 0.6dvw;
  color: var(--el-color-info-light-3);
  gap: 0.5dvw;
}

.loading-icon {
  font-size: 0.7dvw;
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.history-list li {
  padding: 0.3dvw;
  font-size: 0.8dvw;
  line-height: 0.9dvw;
  margin-bottom: 0.4dvw;
  border: 0.15dvw solid transparent;
  background-origin: border-box;
  background-clip: padding-box, border-box;
  box-sizing: border-box;
}

.history-list li:hover {
  border: 0.15dvw solid rgba(200, 200, 200, 0.4);
  border-radius: 0.4dvw;
  background: linear-gradient(135deg,
      rgba(200, 200, 200, 0.15) 0%,
      rgba(160, 160, 160, 0.1) 100%);
  cursor: pointer;
}

/* 历史对话选中状态样式 */
.history-list li.active {
  border: 0.15dvw solid rgba(160, 160, 160, 0.6);
  border-radius: 0.4dvw;
  background: linear-gradient(135deg,
      rgba(200, 200, 200, 0.4) 0%,
      rgba(160, 160, 160, 0.3) 100%);
  cursor: pointer;
}

/* 历史对话选中状态的hover样式 */
.history-list li.active:hover {
  border: 0.15dvw solid rgba(160, 160, 160, 0.8);
  background: linear-gradient(135deg,
      rgba(200, 200, 200, 0.5) 0%,
      rgba(160, 160, 160, 0.4) 100%);
}

/* 对话项布局 */
.conversation-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 0.3dvw;
}

.conversation-content {
  flex: 1;
  min-width: 0;
  cursor: pointer;
}

.conversation-title {
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 选中状态下的标题样式 */
.conversation-item.active .conversation-title {
  color: rgba(51, 51, 51, 0.95);
  font-weight: 500;
}

/* 重命名输入框 */
.rename-input {
  width: 100%;
}

.rename-input :deep(.el-input__wrapper) {
  background: linear-gradient(135deg,
      rgba(200, 200, 200, 0.15) 0%,
      rgba(160, 160, 160, 0.1) 100%);
  border: 1px solid rgba(200, 200, 200, 0.4);
  border-radius: 0.2dvw;
  padding: 0.1dvw 0.3dvw;
  backdrop-filter: blur(5px);
}

.rename-input :deep(.el-input__inner) {
  color: #333;
  font-size: 0.8dvw;
  line-height: 0.9dvw;
}

/* 三点菜单按钮 */
.conversation-menu {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.conversation-item:hover .conversation-menu {
  opacity: 1;
}

.menu-button {
  padding: 0.2dvw;
  color: rgba(51, 51, 51, 0.6);
  outline: 0px solid transparent;
  background: transparent;
  font-size: 0.7dvw;
  min-height: auto;
  height: auto;
}

.menu-button:hover {
  color: rgba(51, 51, 51, 0.9);
  background: linear-gradient(135deg,
      rgba(200, 200, 200, 0.2) 0%,
      rgba(160, 160, 160, 0.15) 100%);
  outline: 1px solid rgba(200, 200, 200, 0.3);
}

/* 下拉菜单样式 */
.conversation-dropdown-menu {
  background: linear-gradient(145deg,
      rgba(255, 255, 255, 0.95) 0%,
      rgba(200, 200, 200, 0.1) 100%);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(200, 200, 200, 0.3);
  border-radius: 0.4dvw;
  box-shadow: 0 4px 16px rgba(160, 160, 160, 0.15);
  min-width: 5dvw;
}

.conversation-dropdown-menu .el-dropdown-menu__item {
  display: flex;
  align-items: center;
  padding: 0.4dvw 0.6dvw;
  font-size: 0.7dvw;
  color: #333;
}

.conversation-dropdown-menu .el-dropdown-menu__item:hover {
  background: linear-gradient(135deg,
      rgba(200, 200, 200, 0.2) 0%,
      rgba(160, 160, 160, 0.15) 100%);
}

.rename-item {
  color: #f56c6c !important;
}

.delete-item {
  color: #666 !important;
}

/* 底部区域 */
.slider-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1dvw;
  border-top: 1px solid rgba(200, 200, 200, 0.2);
  margin-top: auto;
  /* 确保底部区域在最下面 */
}

.slider.collapsed .slider-footer {
  flex-direction: column;
  gap: 0.6dvw;
  padding: 0.8dvw;
  align-items: center;
}

/* 反馈按钮 */
.feedback-button {
  display: flex;
  align-items: center;
  gap: 0.8dvw;
  padding: 0.6dvw 1.2dvw;
  font-size: 0.8dvw;
  color: white;
  border: none;
  border-radius: 0.4dvw;
  background: linear-gradient(135deg, #a0a0a0 0%, #808080 100%);
  backdrop-filter: blur(15px);
  transition:
    background 0.3s ease,
    transform 0.3s ease,
    box-shadow 0.3s ease;
}

.slider.collapsed .feedback-button {
  width: 2dvw;
  height: 2dvw;
  padding: 0;
  gap: 0;
  border-radius: 0.3dvw;
  min-height: auto;
}

.feedback-button:hover {
  color: white;
  background: linear-gradient(135deg, #b0b0b0 0%, #909090 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(160, 160, 160, 0.4);
}

/* 折叠按钮 */
.collapse-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2dvw;
  height: 2dvw;
  padding: 0;
  color: rgba(51, 51, 51, 0.7);
  background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.8) 0%,
      rgba(200, 200, 200, 0.2) 100%);
  border: 1px solid rgba(200, 200, 200, 0.3);
  border-radius: 0.3dvw;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  margin: 0;
}

.collapse-button:hover {
  color: rgba(51, 51, 51, 0.9);
  background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.9) 0%,
      rgba(200, 200, 200, 0.3) 100%);
  border-color: rgba(200, 200, 200, 0.5);
  box-shadow: 0 2px 8px rgba(160, 160, 160, 0.2);
  transform: translateY(-1px);
}

/* 折叠状态下按钮图标统一大小 */
.slider.collapsed .feedback-button .el-icon,
.slider.collapsed .collapse-button .el-icon {
  font-size: 1dvw;
}

.app-user-info {
  display: flex;
  align-items: center;
  gap: 0.6dvw;
  font-weight: bold;
  font-size: 0.8dvw;
}

.user-dropdown {
  cursor: pointer;
}

.user-avatar {
  width: 4.7dvh;
  height: 4.7dvh;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.user-avatar:hover {
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
  transform: scale(1.05);
}

.user-dropdown-menu {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  min-width: 6dvw;
}