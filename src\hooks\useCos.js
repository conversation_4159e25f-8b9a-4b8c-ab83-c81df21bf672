import CosSdk from 'cos-js-sdk-v5';
import cosUtil from 'cos-js-sdk-v5/src/util';
import { http } from '@/extensions/http';
import { httpApis } from '@/hooks/useHttpApi';
import dayjs from 'dayjs';
import { ref } from 'vue';
const DOMAIN = 'vet-ai-file.xw-vet.com';

const stsStore = ref({});

let cosInstance = null;
function getCos() {
    return new CosSdk({
        // Domain: DOMAIN,
        getAuthorization(options, cb) {
            checkCosAuthorization().then(cb);
        }
    });
}

/**
 * 检查sts的可用性，在调用cos.updateFile之前进行调用
 * @return {Promise}
 */
export function checkCosAuthorization() {
    if (stsStore.value.ExpiredTime && Date.now() < stsStore.value.ExpiredTime * 1000 - 2000) {
        return Promise.resolve(stsStore.value);
    }
    return http.get(httpApis.getCosSts).then(res => {
        const data = res.data;
        const credentials = data.credentials;
        stsStore.value = {
            TmpSecretId: credentials.tmpSecretId,
            TmpSecretKey: credentials.tmpSecretKey,
            XCosSecurityToken: credentials.sessionToken,
            StartTime: data.startTime,
            ExpiredTime: data.expiredTime,
            bucket: data.bucket,
            // Domain: data.urlPrefix,
            region: data.region,
            prefix: data.pathPrefix
        };
        return stsStore.value;
    });
}

/**
 * 基于文件创建文件路径
 * @param {File} file
 * @param {string} module
 * @returns {Promise}
 */
export function createKeyWithFile(file, module = 'files') {
    return new Promise((resolve, reject) => {
        cosUtil.getFileMd5(file, (err, hash) => {
            if (err) {
                reject(err);
                return;
            }
            const datePath = dayjs().format('YYYY/MM/DD');
            const ext = (file.name.split(/\./g).pop()).toLowerCase();
            resolve(`${stsStore.value.prefix}/${module}/${datePath}/${hash}.${ext}`);
        });
    });
}
export function getUrl(location) {
    let path = location.substring(location.indexOf('/') + 1);
    if (/^https[s]?:\/\//.test(DOMAIN)) {
        return `${DOMAIN}/${path}`;
    }
    return `https://${DOMAIN}/${path}`;
}
/**
 * 获取预授权可以访问的URL地址
 * @link https://cloud.tencent.com/document/product/436/11459#.E4.B8.8B.E8.BD.BD.E5.AF.B9.E8.B1.A1
 * @param {string} key 访问路径
 * @param {boolean?} download 是否需要下载
 * @param {string?} filename 下载文件重命名
 * @returns {Promise<string>}
 */
export function getCosObjectUrl(key, download = false, filename) {
    return new Promise((resolve, reject) => {
        let url = key;
        const cos = useCos();
        if (/^http[s]*:\/\//.test(key)) {
            key = key.replace(/^http[s]*:\/\/[^/]+\//gi, '');
        }
        if (/^\w+\/public\//.test(key)) {
            if (download) {
                url = url + (url.indexOf('?') > -1 ? '&' : '?') + 'response-content-disposition=attachment';
                if (filename) {
                    url += ';filename=' + filename;
                }
            }
            return resolve(url);
        }
        cos.getObjectUrl({
            Bucket: stsStore.value.bucket,
            Region: stsStore.value.region,
            Key: key,
            Expires: 3600,
            Sign: true
        }, function (err, data) {
            if (err) {
                reject(err);
                return;
            }
            let url = data.Url;
            if (download) {
                url = url + (url.indexOf('?') > -1 ? '&' : '?') + 'response-content-disposition=attachment';
                if (filename) {
                    url += ';filename=' + filename;
                }
            }
            resolve(url);
        });
    });
}

/**
 * 构建上传参数
 * @param params
 * @return {any}
 */
export function createUploadFileParams(params) {
    return Object.assign({
        Bucket: stsStore.value.bucket,
        Region: stsStore.value.region,
    }, params);
}
/**
 * 上传文件到COS
 * @param {File | Blob | String} file 需要上传的文件
 * @param {COS.UploadFileParams} options 配置项
 * @return {Promise<string>} 上传后返回真实的URL地址
 */
export const uploadCosFile = async function (file, options = {}, module) {
    await checkCosAuthorization();
    const cos = useCos();
    const key = options.key || await createKeyWithFile(file, module);
    return cos.uploadFile(Object.assign({
        Bucket: stsStore.value.bucket,
        Region: stsStore.value.region,
        Body: file,
        Key: key,
        onProgress: e => {
            if (typeof options.onProgress === 'function') {
                options.onProgress.call(null, e);
            }
        }
    }, options)).then(res => {
        return {
            key,
            url: getUrl(res.Location)
        };
        // return getCosObjectUrl(key);
    });
};

/**
 * 获取CosSdk实例
 * @returns { CosSdk }
 */
export function useCos() {
    if (!cosInstance) {
        cosInstance = getCos();
    }
    return cosInstance;
}
