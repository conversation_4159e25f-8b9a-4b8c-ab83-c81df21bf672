<template>
    <div class="marked">
        <div ref="messagesRef"></div>
    </div>
</template>

<script setup>
import { createApp, onMounted, reactive, ref, watchEffect } from 'vue';
import ChatMessage from './ChatMessage.vue';
import think from '@/components/chat-widgets/think.vue';
import mark from '@/components/chat-widgets/mark.vue';
import llmCite from '@/components/chat-widgets/cite.vue';
import ElementPlus from 'element-plus';
import ui from '@/ui/index.js';
import MarkdownContainer from '@/components/markdown/MarkdownContainer.vue';
const messagesRef = ref(null);
const parts = reactive([]);
const props = defineProps({
    content: {
        type: String,
        default: 'xxx'
    }
});
onMounted(() => {
    const app = createApp(ChatMessage, {
        parts: parts,
        isDone: true
    });
    app.component('LlmThink', think);
    app.component('LlmMark', mark);
    app.component('LlmCite', llmCite);
    app.component('MarkdownContainer', MarkdownContainer);
    app.use(ElementPlus);
    app.use(ui);
    app.mount(messagesRef.value);
});
watchEffect(() => {
    parts.splice(0);
    parts.push(props.content);
});
</script>

<style scoped lang="scss">

</style>
