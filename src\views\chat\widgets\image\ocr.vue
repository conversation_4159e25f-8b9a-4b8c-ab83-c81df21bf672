<template>
    <div class="image-ocr">
        <div class="ocr">
            <div class="files">
                <el-upload
                    class="image-uploader"
                    drag
                    multiple
                    accept="image/*"
                    :http-request="handleUpload"
                    :show-file-list="false"
                    :auto-upload="false"
                    @change="onFileChange"
                >
                    <div>
                        <rp-icon name="upload" size="30" />
                        <div class="el-upload__text">将文件拖到此处，或 <em>点击上传</em></div>
                        <div class="el-upload__tip">支持截图粘贴、拖拽、多文件</div>
                    </div>
                </el-upload>
                <div class="image-list">
                    <div class="image-item" v-for="(row) in fileList" :key="row.value.index">
                        <el-image
                            style="width: 70px;height: 70px;"
                            :src="row.value.url" fit="contain"
                            show-progress
                            :preview-src-list="filePreviewList"
                        />
                        <span class="status running" v-if="row.value.status===1">
                            <rp-icon name="loading" class="is-loading" />
                            提取中
                        </span>
                        <span class="status success" v-else-if="row.value.status===2">提取完成</span>
                        <span class="status error" v-else>提取失败</span>
                    </div>
                </div>
            </div>
            <div class="ocr-result">
                <div class="ocr-result-list">
                    <div class="report-result" v-for="(report, idx) in recognizeTaskResult" :key="idx">
                        <div class="pet" v-if="report.pet">
                            {{ report.pet.name }} {{ report.pet.gender }} {{ report.pet.age }} {{ report.pet.species }} {{ report.pet.breed }} {{ report.pet.weight }}
                        </div>
                        <div class="row"><span class="title">检查项目：</span>{{ report.reportProjectName }}</div>
                        <div class="row"><span class="title">检查部位/体位：</span>{{ report.checkLocation }}</div>
                        <div class="row">
                            <span class="title">检查所见：</span>
                            <pre>{{ report.examinationFindings }}</pre>
                        </div>
                        <div class="row">
                            <span class="title">检查印象：</span>
                            <pre>{{ report.conclusion }}</pre>
                        </div>
                        <div class="row"><span class="title">检查日期：</span>{{ report.checkDate }}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="el-dialog__footer">
            <el-button @click="$emit('close')">取消</el-button>
            <el-button type="primary" @click="sendMessage" :disabled="recognizeTaskResult.length===0">上传完成</el-button>
        </div>
    </div>
</template>

<script setup>
import { computed, onMounted, onUnmounted, reactive, ref, toRaw, unref } from 'vue';
import { file2base64 } from '@/utils/index';
import { http } from '@/extensions/http';
import { httpApis } from '@/hooks/useHttpApi';
import EditableText from '@/ui/form/EditableText.vue';
import RpIcon from '@/ui/icon/icon.vue';
import { uploadCosFile } from '@/hooks/useCos.js';
const props = defineProps({
    resultType: {
        type: String,
        default: 'markdown' // markdown | json
    },
    uploadType: {
        type: String,
        default: 'base64' // base64 | url
    }
});
const emit = defineEmits(['ok']);

// const uploadList = ref([]);
const fileList = ref([]);
let taskIndex = 1;
// const reportItems = reactive([]);
const recognizeTaskResult = reactive([
]);
// uploadList.value.push({
//     key: 1,
//     url: 'http://rppet-fe-1312829983.cos.ap-shanghai.myqcloud.com/ai/vet/dev/files/2025/04/21/4f9935bb11805d5a20f7e472e3ab7a6c.png'
// }, {
//     key: 2,
//     url: 'http://rppet-fe-1312829983.cos.ap-shanghai.myqcloud.com/ai/vet/dev/files/2025/04/21/1250c63f7fb535236b5e3a5a123f9439.png'
// });
const filePreviewList = computed(() => {
    return fileList.value.map(item => {
        return item.value.url;
    });
});
const sendMessage = () => {
    if (recognizeTaskResult.length === 0) {
        return;
    }
    if (props.resultType === 'markdown') {
        const result = recognizeTaskResult.map(item => {
            const md = `**检查项目**：${item.reportProjectName}
**检查部位/体位**：${item.checkLocation}
**检查日期**：${item.checkDate}
### 检查所见
${item.examinationFindings}`;
            return {
                pet: toRaw(item.pet),
                text: md,
                image: item.content.result
            };
        });
        emit('ok', result);
    }
    else {
        const result = recognizeTaskResult.map(item => {
            return {
                pet: toRaw(item.pet),
                reportProjectName: item.reportProjectName,
                checkLocation: item.checkLocation,
                examinationFindings: item.examinationFindings,
                conclusion: item.conclusion,
                checkDate: item.checkDate,
                image: item.url
            };
        });
        emit('ok', result);
    }
};
// 处理上传
const handleUpload = async ({
    file
}) => {
    try {
        await createRecognizeTask(file);
    }
    catch (err) {
        console.log(err);
        // ElMessage.error('上传失败');
    }
};

const createRecognizeTask = async (file) => {
    const task = ref({
        index: taskIndex++,
        status: 1, // 1：运行中 2：成功 3：失败
        // content: res,
        items: [],
        pet: null,
        remark: ''
    });
    if (props.uploadType === 'base64') {
        const res = await file2base64(file);
        task.value.content = res;
        task.value.url = res.result;
    }
    else {
        const res = await uploadCosFile(file, {}, 'image-report');
        task.value.url = res.url;
    }
    fileList.value.push(task);
    recognize(task);
};
/**
 * 图片指标识别
 * @param task
 */
const recognize = (task) => {
    let params = {};
    if (props.uploadType === 'base64') {
        params = {
            base64: task.value.content.base64,
            mineType: task.value.content.mineType
        };
    }
    else {
        params = {
            url: task.value.url
        };
    }
    http.post(httpApis.recognizeImageReport, params, {
        timeout: 0
    }).then(res => {
        console.log(res.data);
        const report = res.data;
        if (report.examinationFindings) {
            task.value.status = 2;
            task.value.reportProjectName = report.reportProjectName;
            task.value.pet = report.pet;
            task.value.checkLocation = report.checkLocation;
            task.value.examinationFindings = report.examinationFindings;
            task.value.conclusion = report.conclusion;
            task.value.checkDate = report.checkDate;
            recognizeTaskResult.push(unref(task));
        }
        else {
            task.value.status = 3;
        }
    }).catch(e => {
        console.log(e);
        task.value.status = 3;
    });
};

// 粘贴上传
const handlePaste = async (event) => {
    const items = event.clipboardData?.items;
    if (!items) return;

    for (const item of items) {
        if (item.type.startsWith('image/')) {
            const file = item.getAsFile();
            if (file) {
                await handleUpload({
                    file
                });
            }
        }
    }
};

// 拦截 input 文件变化
const onFileChange = (upload) => {
    const files = upload?.target?.files || upload.raw;
    if (!files) return;

    const list = Array.isArray(files) ? files : [files];
    list.forEach(async file => {
        await handleUpload({
            file
        });
    });
};

onMounted(() => {
    window.addEventListener('paste', handlePaste);
});
onUnmounted(() => {
    window.removeEventListener('paste', handlePaste);
});
</script>

<style scoped lang="scss">
.ocr {
    max-height: 600px;
    overflow-y: auto;
    display: flex;
    flex-wrap: wrap;
}
.files {
    width: 250px;
}
.ocr-result {
    flex: 1;
    margin-left: 10px;
    height: 100%;
    overflow-y: auto;
    min-width: 300px;
    .report-result {
        .row {
            color: #333;
            .title {
                color: #111;
                font-weight: 500;
            }
            pre {
                white-space: pre-wrap;
            }
        }
        &+.report-result {
            margin-top: 10px;
            border-top: 1px solid #ddd;
        }
    }
}
.image-uploader {
    border-radius: 6px;
    text-align: center;
    cursor: pointer;
}
.input-box {
    margin-bottom: 10px;
}
.image-list {
    margin-top: 10px;
    display: grid;
    gap: 10px;
    grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));
    .image-item {
        border: 1px solid #ddd;
        border-radius: 5px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        img {
            max-width: 70px;
            height: 70px;
        }
        .status {
            margin-top: 4px;
            font-size: 12px;
            &.running {}
            &.success {
                color: #67c23a;
            }
            &.error {
                color: #f56c6c;
            }
        }
    }
}
</style>
