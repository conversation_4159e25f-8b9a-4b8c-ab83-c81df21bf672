<script lang="ts" setup>
import { useSliderStore } from "@/stores/slider";
import { storeToRefs } from "pinia";
import NewChatIcon from "@/components/icons/NewChatIcon.vue";
import { useChatStore } from "@/stores/chat";

// 全局的Pina库
const sliderStore = useSliderStore();
const chatStore = useChatStore();

// 全局的响应数据
const { curFunction } = storeToRefs(sliderStore);
const { messageList } = storeToRefs(chatStore);

// 全局方法
const { clickNewChat } = sliderStore;
</script>

<template>
  <div class="header-container">
    <div class="header-left"></div>
    <div class="header-center">
      {{ curFunction.title }}
    </div>
    <div class="header-right">
      <button class="new-chat-button" @click="clickNewChat" v-if="messageList.length > 0">
        <NewChatIcon style="font-size: 0.8dvw;"/>
        新对话
      </button>
    </div>
  </div>
</template>
<style scoped src="./ChatHeader.css"></style>
