<template>
    <div class="editable-text-container">
        <div @click="edit" v-if="!isEditing" class="editable-text">
            {{ modelValue || '' }}
        </div>

        <input
            v-else
            ref="inputRef"
            v-model="inputValue"
            @blur="onBlur"
            @keyup.enter="onBlur"
            class="editable-input"
        >
    </div>
</template>

<script setup>
import { ref, nextTick } from 'vue';

defineOptions({
    name: 'EditableText'
});
// Props & Emits
const props = defineProps({
    modelValue: String
});
const emit = defineEmits(['update:modelValue', 'change']);

// 状态
const isEditing = ref(false);
const inputValue = ref('');
const inputRef = ref(null);

// 进入编辑状态
function edit() {
    isEditing.value = true;
    inputValue.value = props.modelValue || '';
    nextTick(() => {
        inputRef.value?.focus();
    });
}

// 退出编辑状态
function onBlur() {
    isEditing.value = false;
    if (inputValue.value !== props.modelValue) {
        emit('update:modelValue', inputValue.value);
        emit('change', inputValue.value);
    }
}
</script>

<style scoped lang="scss">
.editable-text-container {
    width: 100%;
    min-width: 100px;
}
.editable-text {
    cursor: pointer;
    padding: 4px 8px;
    width: 100%;
    border: 1px dashed transparent;
    border-radius: 5px;
    min-height: 30px;
    box-sizing: border-box;
}
.editable-text:hover {
    border-color: #dcdfe6;
    background-color: #f5f7fa;
}
.editable-input {
    padding: 4px 8px;
    width: 100%;
    border-radius: 4px;
    font-size: inherit;
    border: none !important;
    background: none !important;
}
</style>
