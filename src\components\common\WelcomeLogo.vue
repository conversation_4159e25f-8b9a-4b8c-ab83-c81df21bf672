<script lang="ts" setup>
import { useSliderStore } from '@/stores/slider';
import { storeToRefs } from 'pinia';


const sliderStore = useSliderStore();
const {
    curFunction
} = storeToRefs(sliderStore);

</script>

<template>
    <div class="welcome-container">
        <div class="welcome-main">
            <img src="/logo.png" alt="Logo" class="welcome-logo" />
            <h2 class="welcome-title">{{ curFunction.subTitle }}</h2>
            <p class="welcome-subtitle">
                {{ curFunction.description }}
            </p>
        </div>
    </div>
</template>
<style scoped src="./WelcomeLogo.css"></style>
