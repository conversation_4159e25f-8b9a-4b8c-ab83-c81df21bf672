<template>
    <div class="vma-history">
        <div class="title">历史记录</div>
        <div class="list">
            <div
                class="item" v-for="(record, index) in records" :key="index"
                :class="{active: +route.params.id===record.id}"
                @click="goMedicalDetail(record.id)"
            >
                <div class="item-header">
                    <div>#{{ record.id }}</div>
                </div>
                <div class="item-content">
                    <div class="item-title">
                        <div class="item-title-text">{{ record.petName || '暂无名称' }}</div>
                    </div>
                    <div class="item-date">{{ record.createdAt }}</div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { onMounted, onUnmounted, ref } from 'vue';
import { http } from '@/extensions/http';
import { httpApis } from '@/hooks/useHttpApi';
import { useRouter } from 'vue-router';
import { useRoute } from 'vue-router';
import { useEventBus } from '@vueuse/core';
const route = useRoute();
const bus = useEventBus('voiceMedicalRecord');
bus.on((event) => {
    if (event === 'newRecord') {
        loadHistory();
    }
});
const emit = defineEmits(['change']);
const router = useRouter();
const records = ref([]);
onMounted(() => {
    loadHistory();
});
onUnmounted(() => {
    bus.off();
});
const loadHistory = () => {
    return http.get(httpApis.listMyMedicalRecord, {
        params: {
            size: 100
        }
    }).then(res => {
        console.log(res.data);
        records.value = res.data.records;
    });
};
const goMedicalDetail = (id) => {
    router.push({
        name: 'VmaDetail',
        params: {
            id: id
        }
    });
    emit('change', id);
};
</script>

<style scoped lang="scss">
.vma-history {
    flex: 1;
    overflow: hidden;
    .title {
        padding: 8px 16px;
        height: 40px;
    }
    .list {
        height: 100%;
        overflow: auto;
        padding: 0 8px;
    }
    .item {
        padding: 4px 8px;
        border-radius: 5px;
        cursor: pointer;
        &:hover {
            background: #e1effd;
        }
        &.active {
            background-color: #cce6ff;
        }
    }
    .item-content {
        display: flex;
        .item-title {
            flex: 1;
        }
        .item-title-text {
            font-weight: 500;
        }
        .item-date {
            font-size: 12px;
            color: #999;
        }
    }
}
</style>
