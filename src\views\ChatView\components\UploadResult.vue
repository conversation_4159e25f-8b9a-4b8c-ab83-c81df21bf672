<script setup lang="ts">
import type { ImageAttachment } from "@/services/chatService";
import { OcrService } from "@/services/ocrService";
import {
  extractTablesFromMarkdown,
  parseContentBlocks,
  parseMarkdownTable,
  type TableData,
} from "@/utils/markdownUtils";
import { type UploadRequestOptions } from "element-plus";
import { computed, ref } from "vue";
import { useMarkdownRenderer } from "@/hooks/useMarkdown";
import EditableTable from "./EditableTable.vue";
import "@/styles/markdown.scss";

// Props
interface Props {
  modelValue: boolean;
}

// Emits
interface Emits {
  (e: "update:modelValue", value: boolean): void;
  (e: "complete", files: ImageAttachment[]): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const ImageAttachments = ref<ImageAttachment[]>([]);
const curFile = ref<ImageAttachment>();
const loadingUids = ref<number[]>([]);

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit("update:modelValue", value),
});

const loading = computed((): boolean => {
  return !!(loadingUids.value && loadingUids.value?.length > 0);
});

const successFiles = computed(() => {
  return ImageAttachments.value.filter((file) => file.status === "success");
});

const showFile = computed(() => {
  return curFile.value || successFiles.value[0] || null;
});

const handleUpload = async (options: UploadRequestOptions): Promise<any> => {
  const { file, onSuccess, onError } = options;
  let data: any = {};
  try {
    // 添加到loading列表
    loadingUids.value.push(file.uid);
    const base64 = await OcrService.fileToBase64(file);
    data.base64 = base64;
    const response = await OcrService.ocrText(base64);
    onSuccess(response.result);
    // 保存完整的OCR文本
    data.ocrText = response.result?.text || "";
    // 解析内容块（保持原始顺序）
    data.contentBlocks = parseContentBlocks(response.result?.text || "");
    // 为了兼容性，仍然提取表格内容
    data.tableContent = extractTablesFromMarkdown(response.result?.text || "");
  } catch (err: any) {
    onError(err);
  } finally {
    // 从loading列表中移除
    loadingUids.value = loadingUids.value.filter((uid) => uid !== file.uid);
    return data;
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  ImageAttachments.value = [];
  loadingUids.value = [];
};

// 完成上传
const handleComplete = () => {
  emit("complete", successFiles.value);
  handleClose();
};

// 选择文件显示OCR文本
const handleSelectFile = (file: ImageAttachment) => {
  curFile.value = file;
};

// 预览图片（放大显示）
const handlePreviewImage = (file: ImageAttachment) => {
  // 创建预览容器
  const previewContainer = document.createElement("div");
  previewContainer.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    cursor: pointer;
  `;

  const previewImg = document.createElement("img");
  previewImg.src = file.response.base64;
  previewImg.style.cssText = `
    max-width: 90%;
    max-height: 90%;
    object-fit: contain;
  `;

  previewContainer.appendChild(previewImg);
  document.body.appendChild(previewContainer);

  // 点击关闭预览
  previewContainer.addEventListener("click", () => {
    document.body.removeChild(previewContainer);
  });
};

// 获取按原始顺序排列的内容块
const contentBlocks = computed(() => {
  return showFile.value?.response?.contentBlocks || [];
});

// 检查是否有内容块
const hasContentBlocks = computed(() => contentBlocks.value.length > 0);

// 创建支持KaTeX的markdown渲染器实例
const md = useMarkdownRenderer();

// 渲染文本内容块
const renderTextBlock = (content: string) => {
  return md.render(content);
};

// 解析表格内容块
const parseTableBlock = (content: string): TableData[] => {
  return parseMarkdownTable(content);
};

// 用于完整OCR内容的渲染（备用）
const parsedOcrText = computed(() => {
  if (!showFile.value?.response?.ocrText) return null;
  return md.render(showFile.value.response.ocrText);
});

// 更新内容块中的表格数据
const handleBlockTableUpdate = (
  updatedMarkdown: string,
  blockIndex: number
) => {
  if (!showFile.value?.response?.contentBlocks) return;

  const blocks = [...showFile.value.response.contentBlocks];
  const targetBlock = blocks[blockIndex];

  if (targetBlock && targetBlock.type === "table") {
    // 更新表格块的内容
    targetBlock.content = updatedMarkdown;

    // 更新当前文件的内容块
    showFile.value.response.contentBlocks = blocks;

    // 重新组合完整的OCR文本
    const combinedContent = blocks.map((block) => block.content).join("\n\n");
    showFile.value.response.ocrText = combinedContent;

    // 同时更新ImageAttachments中对应的文件
    const fileIndex = ImageAttachments.value.findIndex(
      (file) => file.uid === showFile.value?.uid
    );

    if (fileIndex !== -1) {
      ImageAttachments.value[fileIndex].response.contentBlocks = blocks;
      ImageAttachments.value[fileIndex].response.ocrText = combinedContent;
    }
  }
};

// 选择文件显示OCR文本
const handleDeleteFile = (file: ImageAttachment) => {
  // 从ImageAttachments数组中删除指定的文件
  ImageAttachments.value = ImageAttachments.value.filter(
    (item) => item.uid !== file.uid
  );

  // 如果当前选中的文件就是被删除的文件，则清空curFile
  if (curFile.value?.uid === file.uid) {
    curFile.value = undefined;
  }
};
</script>

<template>
  <el-dialog v-model="dialogVisible" title="化验结果上传" width="70%" :before-close="handleClose" top="3vh"
    style="margin-bottom: 0" center>
    <div class="upload-result-dialog">
      <div class="upload-section">
        <div class="upload-content" v-if="successFiles && successFiles.length > 0">
          <!-- 按原始顺序展示内容块 -->
          <div v-if="hasContentBlocks">
            <template v-for="(block, index) in contentBlocks" :key="`block-${index}`">
              <!-- 文本内容块 -->
              <div v-if="block.type === 'text'" class="text-block">
                <div class="marked" v-html="renderTextBlock(block.content)"></div>
              </div>

              <!-- 表格内容块 -->
              <div v-else-if="block.type === 'table'">
                <EditableTable v-for="(table, tableIndex) in parseTableBlock(block.content)"
                  :key="`block-${index}-table-${tableIndex}`" :table-data="table" @update="
                    (markdown: any) => handleBlockTableUpdate(markdown, index)
                  " class="editable-table-wrapper" />
              </div>
            </template>
          </div>

          <!-- 如果没有内容块，显示原始内容（兼容性备用） -->
          <div v-else class="marked" v-html="parsedOcrText"></div>
        </div>
        <div v-else style="height: 50dvh; text-align: center">
          <img style="height: 100%; border-radius: 5dvw" src="/chat/no_upload_result_files.jpeg" />
        </div>
        <el-upload drag multiple v-model:file-list="ImageAttachments" :http-request="handleUpload" :disabled="loading"
          :show-file-list="false">
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            <template v-if="loading">
              正在上传 {{ loadingUids.length }} 个文件，请稍候...
            </template>
            <template v-else>
              拖动化验结果文件到此处 或 <em>点击选择文件</em>
            </template>
          </div>
        </el-upload>
      </div>
      <div class="upload-slider" v-if="successFiles && successFiles.length > 0">
        <ul class="upload-file-list">
          <li v-for="file in successFiles" :key="file.id" class="upload-file-item"
            :class="{ active: curFile?.uid === file.uid }" @click="handleSelectFile(file)">
            <div class="upload-file-image-container">
              <el-image :src="file.response.base64" fit="cover" class="upload-file-image" />
              <div class="upload-file-image-options">
                <el-button class="expand-button" size="small" circle @click.stop="handlePreviewImage(file)">
                  <el-icon><zoom-in /></el-icon>
                </el-button>
                <el-button class="delete-button" size="small" circle @click.stop="handleDeleteFile(file)">
                  <el-icon>
                    <delete />
                  </el-icon>
                </el-button>
              </div>
            </div>
            <el-text class="upload-file-title" truncated>
              {{ file.name }}
            </el-text>
          </li>
        </ul>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" class="cancel-button"> 取消 </el-button>
        <el-button type="primary" @click="handleComplete" class="complete-button" :loading="loading">
          完成 ({{
            ImageAttachments.filter((f) => f.status === "success").length
          }})
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped src="./UploadResult.css"></style>
