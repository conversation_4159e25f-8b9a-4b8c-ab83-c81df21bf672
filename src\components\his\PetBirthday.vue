<template>
    <div class="pet-birthday">
        <el-input
            v-model="form.ageYear"
            :disabled="disabled"
            placeholder="岁"
            style="width: 80px;"
            maxlength="2"
            @input="onAgeChange"
        />
        <span>岁</span>
        <el-input
            v-model="form.ageMonth"
            :disabled="disabled"
            placeholder="月"
            maxlength="2"
            style="width: 80px; margin-left: 5px;"
            @input="onAgeChange"
        />
        <span>月</span>
    </div>
</template>

<script setup>
import { reactive, watch } from 'vue';
import dayjs from 'dayjs';
import { parseAgeFromDate } from '@/utils/index.js';

const props = defineProps({
    modelValue: {
        type: String,
        default: ''
    },
    disabled: {
        type: Boolean,
        default: false
    }
});

const emit = defineEmits(['update:modelValue']);

const form = reactive({
    ageYear: '',
    ageMonth: ''
});

// 当年龄变化时，反推回出生日期
const onAgeChange = () => {
    const now = dayjs();
    let year = parseInt(form.ageYear) || 0;
    let month = parseInt(form.ageMonth) || 0;

    let birthDate = now.subtract(year, 'year').subtract(month, 'month');

    emit('update:modelValue', birthDate.format('YYYY-MM-DD 00:00:00'));
};

// 监听传进来的modelValue变化
watch(
    () => props.modelValue,
    (newVal) => {
        const res = parseAgeFromDate(newVal);
        form.ageYear = res.ageYear;
        form.ageMonth = res.ageMonth;
    },
    {
        immediate: true
    }
);
</script>

<style scoped lang="scss">
.pet-birthday {
    display: flex;
    align-items: center;
}
</style>
