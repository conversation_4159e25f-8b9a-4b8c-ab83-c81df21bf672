import { ChatService, type ChatMessage, type ImageAttachment } from "@/services/chatService";
import type { Pagination } from "@/services/common";
import { ConversationService } from "@/services/conversationService";
import { ElMessage } from "element-plus";
import { defineStore } from "pinia";
import { ref } from "vue";

export const useChatStore = defineStore("chat", () => {
  const messageList = ref<ChatMessage[]>([]);
  const messageLoading = ref(false);
  const messagePagination = ref<Pagination>({
    page: 1,
    size: 10,
  });
  const uploadResultImages = ref<ImageAttachment[]>([]);
  const uploadReportImages = ref<ImageAttachment[]>([]);
  const isAiTyping = ref(false);

  // 获取历史消息
  const getMessageList = async (conversationId: string, page?: number) => {
    try {
      messageLoading.value = true;
      const result = await ConversationService.getChatHistoryFromApi(
        conversationId,
        page
      );
      messageList.value =
        result.items.map((item: any) => ({
          ...item,
          thinking: item.content.includes("</think>")
            ? item.content.split("</think>")[0]
            : undefined,
          content: item.content.includes("</think>")
            ? item.content.split("</think>")[1]
            : item.content,
          isLike: item.reaction_summary.has_liked,
          isDislike: item.reaction_summary.has_disliked,
          type: item.role === 1 ? "user" : "ai",
        })).reverse() || [];
      messagePagination.value = result.pagination;
    } finally {
      messageLoading.value = false;
    }
  };

  // 加载更多历史消息
  const loadMoreMessageList = async (conversationId: string, page?: number) => {
    try {
      if (messageLoading.value) {
        ElMessage.warning("加载太频繁，请稍候...");
        return;
      }
      messageLoading.value = true;
      const result = await ConversationService.getChatHistoryFromApi(
        conversationId,
        page
      );

      // 将新消息添加到列表开头（因为历史消息是按时间倒序的）
      let newMessages = result.items.map((item: any) => ({
        ...item,
        thinking: item.content.includes("</think>")
          ? item.content.split("</think>")[0]
          : undefined,
        content: item.content.includes("</think>")
          ? item.content.split("</think>")[1]
          : item.content,
        isLike: item.reaction_summary.has_liked,
        isDislike: item.reaction_summary.has_disliked,
      })).reverse();

      // 合并消息列表，去重
      let combinedList = [...newMessages, ...messageList.value];
      if (combinedList.length > 0) {
        const map = new Map();
        combinedList.forEach((item) => {
          if (!map.has(item.id)) {
            map.set(item.id, item);
          }
        });
        combinedList = Array.from(map.values());
      }

      messageList.value = combinedList;
      messagePagination.value = result.pagination;
    } finally {
      messageLoading.value = false;
    }
  };

  /**
   * 点赞消息
   * @param messageId 消息ID
   */
  const likeMessage = async (messageId: number) => {
    try {
      await ChatService.likeMessageFromApi(messageId);
      // 更新messageList中对应消息的状态
      const messageIndex = messageList.value.findIndex(
        (msg) => msg.id === messageId
      );
      if (messageIndex !== -1) {
        messageList.value[messageIndex].isLike = true;
        messageList.value[messageIndex].isDislike = false; // 点赞时取消不喜欢状态
      }
    } catch (error) {
      console.error("点赞失败:", error);
      throw error;
    }
  };

  /**
   * 取消点赞消息
   * @param messageId 消息ID
   */
  const cancelLikeMessage = async (messageId: number) => {
    try {
      await ChatService.cancelLikeMessageFromApi(messageId);
      // 更新messageList中对应消息的状态
      const messageIndex = messageList.value.findIndex(
        (msg) => msg.id === messageId
      );
      if (messageIndex !== -1) {
        messageList.value[messageIndex].isLike = false;
      }
    } catch (error) {
      console.error("取消点赞失败:", error);
      throw error;
    }
  };

  /**
   * 不喜欢消息
   * @param messageId 消息ID
   */
  const dislikeMessage = async (messageId: number, dislikeType?: number, dislikeDesc?: string, helpfulAdvice?: string) => {
    try {
      await ChatService.dislikeMessageFromApi(messageId, dislikeType, dislikeDesc,helpfulAdvice);
      // 更新messageList中对应消息的状态
      const messageIndex = messageList.value.findIndex(
        (msg) => msg.id === messageId
      );
      if (messageIndex !== -1) {
        messageList.value[messageIndex].isDislike = true;
        messageList.value[messageIndex].isLike = false; // 不喜欢时取消点赞状态
      }
    } catch (error) {
      console.error("不喜欢失败:", error);
      throw error;
    }
  };

  /**
   * 取消不喜欢消息
   * @param messageId 消息ID
   */
  const cancelDislikeMessage = async (messageId: number) => {
    try {
      await ChatService.cancelDislikeMessageFromApi(messageId);
      // 更新messageList中对应消息的状态
      const messageIndex = messageList.value.findIndex(
        (msg) => msg.id === messageId
      );
      if (messageIndex !== -1) {
        messageList.value[messageIndex].isDislike = false;
      }
    } catch (error) {
      console.error("取消不喜欢失败:", error);
      throw error;
    }
  };

  return {
    messageList,
    messageLoading,
    messagePagination,
    uploadResultImages,
    uploadReportImages,
    isAiTyping,
    getMessageList,
    loadMoreMessageList,
    likeMessage,
    cancelLikeMessage,
    dislikeMessage,
    cancelDislikeMessage,
  };
});
