<template>
    <div
        class="app-wrapper"
        :class="{
            'is-mobile': env.isMobile.value
        }"
    >
        <router-view />
    </div>
</template>

<script setup>
import { useEnv } from '@/hooks/useEnv';
const env = useEnv();
// 解决iPhone上出现滚动条的问题
function setRealVh() {
    const vh = window.innerHeight * 0.01;
    document.documentElement.style.setProperty('--vh', `${vh}px`);
}

setRealVh(); // 初始执行一次
window.addEventListener('resize', setRealVh); // 监听窗口变化
</script>

<style lang="scss">
#app {
    //height: 100vh;
    height: calc(var(--vh, 1vh) * 100); /* fallback 为 100vh */
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
}
.app-wrapper {
    height: 100%;
}

// 覆盖Element Plus的一些默认样式
.el-button--primary {
}

.el-main {
  padding: 0;
}
</style>
