<script lang="ts" setup>
import AppSlider from "@/components/app/AppSlider.vue";
import LoginDialog from "@/components/app/LoginDialog.vue";
import { useCommonStore } from "@/stores/common";
import { TokenCookieManager } from "@/utils/cookieUtils";
import { computed, onMounted } from "vue";
import { useRoute } from 'vue-router';

// 获取当前路由对象
const route = useRoute()

// 全局的Pina库
const commonStore = useCommonStore();

// 本组件的响应式数据
const showLayout = computed(() => {
  // 在登录页和注册页隐藏导航栏
  const hiddenLayoutPath = ['/']
  return hiddenLayoutPath.includes(route.path) ? false : true
})
// 初始化应用状态
const initializeApp = () => {
  const token = TokenCookieManager.getToken();
  if (token) {
    // 如果存在 token，设置到 API 请求头中
    import("@/utils/api").then(({ default: api }) => {
      api.setHeader("Authorization", `Bearer ${token}`);
    });
  }

  // 如果没有 token，显示登录弹框
  if (!commonStore.isLogin) {
    commonStore.showLogin = true;
  }
};

onMounted(() => {
  initializeApp();
});
</script>

<template>
  <div v-if="showLayout" class="page-layout">
    <div class="page-slider">
      <AppSlider />
    </div>
    <div class="page-content">
      <router-view />
    </div>
    <LoginDialog />
  </div>
  <div v-else class="no-page-layout">
    <router-view />
  </div>
</template>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100dvh;
  overflow: hidden;
}

/* 全局滚动条样式 - 悬停显示 */
*::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

*::-webkit-scrollbar-track {
  border-radius: 8px;
  transition: all 0.3s ease;
}

*::-webkit-scrollbar-thumb {
  border-radius: 0.8dvw;
  transition: all 0.3s ease;
}

*:hover::-webkit-scrollbar-thumb {
  background: var(--el-color-info-light-3);
}

body {
  margin: 0;
}

.page-layout {
  display: flex;
  height: 100%;
}

.no-page-layout {
  overflow: auto;
  height: 100%;
}

.page-content {
  width: 100%;
  display: flex;
  flex-direction: column;
}
</style>
