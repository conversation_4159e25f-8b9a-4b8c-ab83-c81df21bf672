import { useBreakpoints } from '@vueuse/core';
import { computed } from 'vue';

const breakpoints = useBreakpoints({
    mobile: 0,
    tablet: 640,
    laptop: 1024,
    desktop: 1280
});
const activeBreakpoint = breakpoints.active();
export const useEnv = () => {
    const isMobile = computed(() => {
        return activeBreakpoint.value === 'mobile';
    });
    return {
        isMobile
    };
};
