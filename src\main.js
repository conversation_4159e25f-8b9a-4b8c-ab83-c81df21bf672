import './extensions/polyfill';
import { createApp } from 'vue';
import ElementPlusX from 'vue-element-plus-x';
import ElementPlus from 'element-plus';
import './style.css';
import './styles/element-plus.scss';
import './styles/iconfont/iconfont.css';
import App from './App.vue';
import router from './router';
import pinia from './stores';
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate';
import ui from './ui/index';
import './styles/main.scss';
import './styles/mobile.scss';
import api from './config/api';
import directives from '@/directives/index';

const app = createApp(App);

app.use(router);
app.use(pinia);
pinia.use(piniaPluginPersistedstate);
app.use(directives);
app.use(ui, router);
app.use(ElementPlusX);
app.use(ElementPlus);
app.config.globalProperties.$api = api;
app.mount('#app');
