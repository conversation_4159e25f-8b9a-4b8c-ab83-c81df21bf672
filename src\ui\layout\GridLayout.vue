<template>
    <div
        class="grid-layout"
        :style="gridStyles"
    >
        <slot></slot>
    </div>
</template>

<script setup>
import { computed } from 'vue';
defineOptions({
    name: 'RpGripLayout'
});
const props = defineProps({
    columns: {
        type: Number,
        default: 3
    },
    gap: {
        type: String,
        default: '10px'
    },
    minWidth: {
        type: String,
        default: null // e.g. '200px' if using responsive grid
    },
    align: {
        type: String,
        default: 'start'
    },
    justify: {
        type: String,
        default: 'start'
    }
});

const gridStyles = computed(() => {
    return {
        display: 'grid',
        gap: props.gap,
        'align-items': props.align,
        'justify-content': props.justify,
        'grid-template-columns': props.minWidth
            ? `repeat(auto-fit, minmax(${props.minWidth}, 1fr))`
            : `repeat(${props.columns}, 1fr)`
    };
});
</script>

<style scoped>
.grid-layout {
    width: 100%;
}
</style>
