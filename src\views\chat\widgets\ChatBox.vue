<template>
    <div
        ref="scrollRef"
        :style="{
            height: props.height
        }"
        class="chat-container"
        :class="{
            'chat-container-empty': currentChat.messages.length === 0 && !props.id
        }"
        @scroll="handleContainerScroll"
        v-show="isChatReady"
    >
        <div class="new-chat-btn" v-if="isChild" style="position: absolute; top: 15px; left: 100px; z-index: 100;">
            <el-button round plain @click="resetChat">
                <rp-icon name="icon-new-chat" />
                <span style="margin-left: 5px;">开启新对话</span>
            </el-button>
        </div>
        <div class="chat-container-inner" :style="{width: props.width}">
            <div class="messages" ref="messagesRef"></div>
        </div>
        <div class="chat-input" :style="{width: props.width}">
            <chat-input
                class="inputbox"
                :settings="chatSettings"
                :is-running="isChatMessageRunning"
                @send-message="handleSendMessage"
                @stop-message="handleStopMessage"
            >
                <div class="empty-state" v-if="currentChat.messages.length === 0 && !props.id">
                    <div>
                        <img src="@/assets/logo-white.png" alt="" style="border-radius: 30px;">
                    </div>
                    <h2 style="margin: 20px 0;font-size: 1.8em;">{{ title }}</h2>
                    <div class="placeholder-text">{{ subtitle }}</div>
                </div>
                <template #bottom>
                    <div class="chat-footer">
                        内容由AI生成，请仔细甄别
                    </div>
                </template>
            </chat-input>
        </div>
    </div>
</template>
<script setup>
import { reactive,
    ref,
    toRef,
    unref,
    createApp,
    onMounted,
    nextTick,
    defineProps,
    watch,
    defineExpose, computed, } from 'vue';
import ElementPlus from 'element-plus';
import ChatMessages from './ChatMessages.vue';
import ChatInput from './ChatInput.vue';
import ui from '@/ui/index';
import { http } from '@/extensions/http';
import { httpApis } from '@/hooks/useHttpApi';
import { useUserStore } from '@/stores/user.js';
import think from '@/components/chat-widgets/think.vue';
import mark from '@/components/chat-widgets/mark.vue';
import llmCite from '@/components/chat-widgets/cite.vue';
import MarkdownContainer from '@/components/markdown/MarkdownContainer.vue';
import { toPlainObject } from '@/utils/index';
import queryCache from './queryCache';
import { useEventBus } from '@vueuse/core';

const userStore = useUserStore();
const chatScene = ref('');
const messagesRef = ref(null);
const scrollRef = ref(null);
const isChatMessageRunning = ref(false);
const isScrollToBottom = ref(false);
const isChatReady = ref(true);
const currentChat = reactive({
    conversationId: '',
    messages: []
});
const bus = useEventBus('chat');
// currentChat.messages.push(toRef({
//     inputs: {},
//     query: '你好！有什么我可以帮忙的吗？',
//     nodeEvent: [],
//     feedback: {
//         rating: null
//     },
//     status: 1,
//     answer: `
// <think>好的，我现在需要处理用户关于猫咪呕吐的问题。首先，我得考虑用户可能的背景，可能是宠物主人，对猫咪的健康状况不太了解，所以需要提供详细但易懂的建议。
//
//
// 用户的问题是“猫咪呕吐怎么办”，这是一个比较常见的症状
// </think>
// \`\`\`mermaid
// graph TD
//     A[发热+厌食] --> B{T4检测结果}
//     B -->|T4>100 nmol/L| C[甲状腺功能亢进症]
//     B -->|T4正常| D{甲状腺超声特征}
//     D -->|血流丰富/不规则| E[甲状腺肿瘤]
//     D -->|血流正常/囊性| F[良性结节/囊肿]
//     A --> G{CRP>35 mg/L}
//     G -->|是| H[细菌性败血症]
//     G -->|否| I{肝功能异常}
//     I -->|ALT/ALP升高| J[肝胆疾病]
//     I -->|正常| K{胰腺炎筛查}
//     K -->|fPL阳性| L[胰腺炎]
//     K -->|阴性| M[免疫介导性疾病]
// \`\`\`
// <div style="color:red;">Run failed: [openai_api_compatible] Server Unavailable Error, HTTPConnectionPool(host='************', port=8888): Max retries exceeded with url: /v1/chat/completions (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x7fc2d9b2d010>: Failed to establish a new connection: [Errno 111] Connection refused'))</div>
//
// 疾病介绍<mark type="sick">oooo</mark> 结束了
//
// \`\`\`
// var a = 1;
// \`\`\`
//
// a > b < d
//
// 这是一个超链接[baidu](https://baidu.com) -- 二号楼
//
// <span class="llm-sick">肠胃炎</span>
//         `
// }));
const props = defineProps({
    isChild: {
        type: Boolean,
        default: false
    },
    id: {
        type: String,
        default: ''
    },
    scene: {
        type: String,
        default: ''
    },
    height: {
        type: String,
        default: '100%'
    },
    width: {
        type: String,
        default: '100%'
    },
    query: {
        type: String,
        default: ''
    },
    title: {
        type: String,
        default: '我是好兽医AI助手，今天我能帮你做什么？'
    },
    subtitle: {
        type: String,
        default: '有什么问题可以随时问我~'
    },
    eventBus: {
        type: Object,
        default: null
    },
    settings: {
        type: Object,
        default: () => {
            return {
                rows: 3,
                tools: {
                    // 化验图片
                    laboratory: true,
                    // 影像图片
                    image: true,
                }
            };
        }
    }
});

const chatSettings = computed(() => {
    if (chatScene.value === 'knowledge') {
        return {
            rows: 3,
            tools: {}
        };
    }
    return props.settings;
});

onMounted(() => {
    chatScene.value = props.scene;
    const app = createApp(ChatMessages, {
        messages: currentChat.messages
    });
    app.component('LlmThink', think);
    app.component('LlmMark', mark);
    app.component('LlmCite', llmCite);
    app.component('MarkdownContainer', MarkdownContainer);
    app.use(ElementPlus);
    app.use(ui);
    app.mount(messagesRef.value);
    if (props.id) {
        currentChat.conversationId = props.id;
        loadMessages(props.id).then(messages => {
            currentChat.messages.push(...messages);
            if (messages.length > 0) {
                chatScene.value = messages[0].inputs.scene || '';
            }
            nextTick(() => {
                isScrollToBottom.value = true;
                scrollToBottom();
            });
        });
    }
    nextTick(() => {
        isScrollToBottom.value = true;
        scrollToBottom();
        if (props.query) {
            // 读取query的缓存数据，避免重复生成
            const cache = queryCache.get(props.query);
            if (cache) {
                currentChat.conversationId = cache.conversationId;
                currentChat.messages.push(...cache.messages);
            }
            else {
                handleSendMessage(props.query, true);
            }
        }
    });
});

const hasMore = ref(false);

watch(() => props.id, (conversationId) => {
    currentChat.messages.splice(0);
    if (conversationId) {
        if (currentChat.conversationId === conversationId) {
            return;
        }
        isChatReady.value = false;
        loadMessages(conversationId).then(messages => {
            // chatMessages.value = messages;
            currentChat.messages.push(...messages);
            nextTick(() => {
                scrollToBottom();
            });
        }).finally(() => {
            isChatReady.value = true;
        });
    }
    currentChat.conversationId = conversationId;
});

const resetChat = () => {
    currentChat.messages.splice(0);
    currentChat.conversationId = '';
};
const loadMessages = (conversationId, firstId = '') => {
    return http.get('/wechat-api/chat/messages', {
        params: {
            conversationId,
            firstId,
            limit: 20
        }
    }).then(res => {
        const json = res.data;
        hasMore.value = json.has_more;
        return json.data.map(item => {
            item.status = 1;
            item.knowledge = {};
            item.feedback = item.feedback || {
                rating: null
            };
            return item;
        });
    });
};
const loadMoreMessages = async () => {
    if (!hasMore.value) {
        return;
    }
    // const first = chatMessages.length > 0 ? chatMessages[0].id : '';
    // const messages = await loadMessages(currentConversation.value.id, first);
    // chatMessages.unshift(...messages);
};
const handleContainerScroll = () => {
    if (scrollRef.value.scrollTop + scrollRef.value.clientHeight >= scrollRef.value.scrollHeight) {
        isScrollToBottom.value = true;
    }
    else {
        isScrollToBottom.value = false;
    }
};
const scrollToBottom = () => {
    if (scrollRef.value && isScrollToBottom.value) {
        scrollRef.value.scrollTop = scrollRef.value.scrollHeight;
    }
};
const handleStopMessage = () => {
    const lastMessage = unref(currentChat.messages[currentChat.messages.length - 1]);
    console.log(lastMessage);
    if (!lastMessage || !lastMessage.taskId) {
        return;
    }
    return http.post(httpApis.stopChatMessage, {
        taskId: lastMessage.taskId
    });
};
const handleSendMessage = (inputMessage, needCache = false) => {
    if (!inputMessage.trim()) {
        return;
    }
    const inputs = {};
    if (chatScene.value) {
        inputs.scene = chatScene.value;
    }
    const message = toRef({
        inputs: {},
        query: inputMessage.trim(),
        answer: '<span><rp-icon name="loading" class="is-loading" />思考中</span>',
        nodeEvent: [],
        feedback: {
            rating: null
        },
        knowledge: {},
        status: 0,
        conversation_id: currentChat.conversationId,
        taskId: null,
        created_at: parseInt(Date.now() / 1000, 10)
    });
    currentChat.messages.push(message);
    isScrollToBottom.value = true;
    const answers = [];
    // 将流中的字节数据解码为文本字符串
    const textDecoder = new TextDecoder();
    function handleMessageTrunk(trunk) {
        message.value.taskId = trunk.task_id;
        switch (trunk.event) {
        case 'queue':
        case 'message_replace':
            answers.splice(0);
            answers.push(trunk.answer);
            break;
        case 'message':
            answers.push(trunk.answer);
            break;
        case 'error':
            message.value.status = 1;
            answers.push(`<div style="color:red;">${trunk.message}</div>`);
            break;
        case 'node_started':
            message.value.nodeEvent.push({
                status: 'running',
                start: trunk.data,
                end: null
            });
            if (trunk.data.title === '知识检索问题重构') {
                answers.splice(0);
                answers.push('<span style="font-size: 12px;">知识检索中…</span>');
            }
            break;
        case 'node_finished':
            if (trunk.data.title === '知识库引用提取') {
                answers.splice(0);
                const outputs = trunk.data.outputs;
                const map = {};
                if (outputs.citations &&  outputs.citations.length > 0) {
                    const list = [];
                    outputs.citations.forEach(item => {
                        const match = item.match(/\[文档(\d+)\]\s*文献名称：(.+)/);
                        if (match) {
                            const idx = match[1];
                            map['文档' + idx] = {
                                index: idx,
                                title: match[2],
                                content: outputs.articles[idx - 1]
                            };
                            list.push(`- [文档${idx}] ${match[2]}`);
                        }
                        message.value.knowledge = map;
                    });
                    answers.push(`<div class="rag-result">检索到${outputs.citations.length}条知识：\n\n${list.join('\n')}</div>\n`);
                }
            }
            message.value.nodeEvent.forEach(item => {
                if (item.start.id === trunk.data.id) {
                    item.end = trunk.end;
                    item.status = 'finished';
                }
            });
            break;
        case 'message_end':
            message.value.status = 1;
            isChatMessageRunning.value = false;
            break;
        }
        const answer = answers.join('');
        if (answer) {
            message.value.answer = answer;
        }
        nextTick(() => {
            scrollToBottom();
        });
        if (needCache && props.query && trunk.event === 'message_end') {
            queryCache.set(props.query, {
                conversationId: currentChat.conversationId,
                messages: toPlainObject(currentChat.messages)
            });
        }
    }
    function decodeValue(data, prepend) {
        const value = textDecoder.decode(data);
        const str = prepend  + value;
        const list = str.split(/\n/);
        const result = [];
        let tail = '';
        list.forEach(row => {
            if (!row) {
                return;
            }
            if (/^data:/g.test(row)) {
                const jsonStr = row.slice(5);
                try {
                    const json = JSON.parse(jsonStr);
                    result.push(json);
                }
                // eslint-disable-next-line no-unused-vars
                catch (e) {
                    tail = row;
                }
            }
            else {
                tail = row;
            }
        });
        return {
            result,
            tail
        };
    }
    let isReady = false;
    isChatMessageRunning.value = true;
    let isNewChat = !currentChat.conversationId;
    http.post(httpApis.sendChatMessage, {
        inputs,
        query: inputMessage.trim(),
        response_mode: 'streaming',
        conversation_id: currentChat.conversationId,
        user: userStore.getUser().id,
        files: []
    }, {
        responseType: 'stream',
        timeout: 0,
        local: true
    }).then(async res => {
        const reader = res.data.getReader();
        let result = true;
        let tail = '';
        while (result) {
            // done表示流是否已经完成读取  value包含读取到的数据块
            const {
                done, value
            } = await reader.read();
            if (done) {
                result = false;
                break;
            }
            // 拿到的value就是后端分段返回的数据，大多是以data:开头的字符串
            // 需要通过decode方法处理数据块，例如转换为文本或进行其他操作
            const decodeResult = decodeValue(value, tail);
            tail = decodeResult.tail;
            decodeResult.result.forEach(trunk => {
                if (!isReady) {
                    message.value.id = trunk.message_id;
                    message.value.conversation_id = trunk.conversation_id;
                    // message.value.answer = '';
                    currentChat.conversationId = trunk.conversation_id;
                }
                handleMessageTrunk(trunk);
            });
            // textDecoder
            //     .decode(value)
            //     .split('\n\n')
            //     .forEach((val) => {
            //         if (!val) return;
            //         // 后端返回的流式数据一般都是以data:开头的字符，排除掉data:后就是需要的数据
            //         // 具体返回结构可以跟后端约定
            //         let text = val?.replace('data:', '') || '';
            //         if (text) {
            //             try {
            //                 const trunk = JSON.parse(text);
            //                 if (!isReady) {
            //                     message.value.id = trunk.message_id;
            //                     message.value.conversation_id = trunk.conversation_id;
            //                     message.value.answer = '';
            //                     currentChat.conversationId = trunk.conversation_id;
            //                 }
            //                 handleMessageTrunk(trunk);
            //             }
            //             catch (e) {
            //                 console.log('解析失败：', val);
            //                 console.error(e);
            //             }
            //         }
            //     });
        }
    }).catch(e => {
        message.value.status = 1;
        message.value.answer = '抱歉，服务出现异常，请稍后重试';
    }).finally(() => {
        isChatMessageRunning.value = false;
        if (isNewChat) {
            bus.emit('newChat');
            if (props.eventBus) {
                props.eventBus.emit('newChat', {
                    id: currentChat.conversationId
                });
            }
        }
    });
};
defineExpose({
    resetChat
});
</script>

<style lang="scss" scoped>
.chat {
    height: 100vh;

    .el-container {
        height: 100%;
    }
}

.chat-container {
    overflow-y: auto;
    .bottom-gap {
        height: 140px;
    }
    &-empty {
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        .messages, .chat-footer {
            display: none;
        }
        .inputbox {
            position: static;
        }
        .bottom-gap {
            height: 0;
        }
        .chat-container-inner {
            min-height: auto;
            padding: 0;
        }
        .new-chat-btn {
            display: none;
        }
    }
    &-inner {
        position: relative;
        margin: 0 auto;
        padding: 20px 0;
        min-height: calc(100% - 140px);
        max-width: 100%;
    }
    .chat-input {
        margin: 0 auto;
        position: sticky;
        bottom: 0;
        padding: 0;
        max-width: 100%;
    }
}
.chat-footer {
    text-align: center;
    font-size: 13px;
    color: #999;
    height: 30px;
    width: 100%;
    line-height: 30px;
}
.empty-state {
    text-align: center;
    margin-bottom: 40px;
}
</style>
