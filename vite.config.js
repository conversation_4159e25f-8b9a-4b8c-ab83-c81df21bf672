import { defineConfig, loadEnv } from 'vite';
import vue from '@vitejs/plugin-vue';
import legacy from '@vitejs/plugin-legacy';
import path from 'path';

// https://vite.dev/config/
export default defineConfig(({
    mode
}) => {
    const env = loadEnv(mode, process.cwd());
    return {
        plugins: [
            legacy({
                targets: ['defaults', 'not IE 11']
            }),
            vue()
        ],
        optimizeDeps: {
            exclude: [
                // 如果仍有问题，可以尝试排除有问题的包
            ]
        },
        resolve: {
            alias: {
                // eslint-disable-next-line no-undef
                '@': path.resolve(__dirname, './src'),
                'vue': 'vue/dist/vue.esm-bundler.js'
            },
            extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx']
        },
        server: {
            host: '0.0.0.0',
            proxy: {
                '/wechat-api': {
                    target: env.VITE_APP_PROXY_CONSOLE_GATEWAY,
                    changeOrigin: true,
                    fwd: true
                    // rewrite: path => path.replace(/^\/api/, '')
                }
            }
        }
    };
});
