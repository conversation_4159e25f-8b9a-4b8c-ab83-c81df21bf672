import Dialog from './dialog.vue';
import { h, render } from 'vue';
class FakePromise {
    constructor(fn) {
        this.resolveQueue = [];
        this.rejectQueue = [];
        this.finallyFn = function () {};
        fn(this.resolve.bind(this), this.reject.bind(this));
    }
    resolve(result) {
        this.resolveQueue.forEach(fn => {
            if (typeof fn === 'function') {
                fn.call(null, result);
            }
        });
        this.finallyFn();
    }
    reject(result) {
        this.rejectQueue.forEach(fn => {
            if (typeof fn === 'function') {
                fn.call(null, result);
            }
        });
        this.finallyFn();
    }
    then(resolveFn, rejectFn) {
        this.resolveQueue.push(resolveFn);
        if (rejectFn) {
            this.rejectQueue.push(rejectFn);
        }
        return this;
    }
    catch(rejectFn) {
        this.rejectQueue.push(rejectFn);
        return this;
    }
    finally(fn) {
        this.finallyFn = fn;
        return this;
    }
}
let AppCache = [];
export default {
    create(app, router) {
        if (router) {
            router.afterEach(() => {
                // 路由调整的时候，将所有的弹窗都销毁
                AppCache.forEach(app => {
                    app.destory();
                });
                AppCache = [];
            });
        }
        // 注册$showDrawer
        function showDrawer(Child, extOptions = {}, dialogOptions = {}) {
            return app.config.globalProperties.$showDialog(Child, extOptions, dialogOptions, true);
        }
        // 注册$showDialog
        function showDialog(
            Child,
            extOptions = {},
            dialogOptions = {},
            isDrawer = false
        ) {
            Child.emits = ['ok', 'close', 'asyncOk', ...(Child.emits || [])];
            // 定义isChild扩展字段，让内容组件中可以有效区分是否为弹窗展示
            if (Child.props && !Child.props.isChild) {
                Child.props.isChild = Boolean;
            }
            // 解决在qiankun子应用中，启用样式隔离后，弹窗中样式无法生效的问题
            if (app._container && app._container.parentNode && app._container.parentNode.dataset) {
                dialogOptions['qiankun'] = app._container.parentNode.dataset.qiankun || 'unknown';
            }
            return new FakePromise((resolve, reject) => {
                // 拷贝
                const NewDialog = Object.create(Dialog);
                // 创建render函数
                let dom = document.createElement('div');
                const vnode = h(NewDialog, {
                    ext: extOptions,
                    dialog: dialogOptions,
                    isDrawer,
                    Child,
                    onOk: function (payload) {
                        resolve(payload);
                        // 延迟.3s 卸载
                        // await new Promise(resolve => setTimeout(resolve,300));
                        // render(null, dom);
                        setTimeout(() => {
                            render(null, dom);
                        }, 300);
                    },
                    onAsyncOk: function (payload, cb) {
                        resolve({
                            payload,
                            cb: function () {
                                cb();
                                setTimeout(() => {
                                    render(null, dom);
                                }, 300);
                            }
                        });
                    },
                    onClose: function () {
                        reject();
                        setTimeout(() => {
                            render(null, dom);
                        }, 300);
                    }
                });
                vnode.appContext = dialogOptions.appContext || app._context;
                console.log(app);
                render(vnode, dom);
                AppCache.push({
                    vnode: vnode,
                    destory: () => {
                        render(null, dom);
                        dom = null;
                    }
                });
            });
        }
        app.config.globalProperties.$showDrawer = showDrawer;
        app.config.globalProperties.$showDialog = showDialog;
        return {
            showDrawer,
            showDialog
        };
    }
};
