<template>
    <div
        class="appx" :class="{
            'is-collapsed': isSidebarCollapsed
        }"
    >
        <div class="menu-item" @click="openNewChat">
            <el-tooltip
                effect="dark" content="辅助鉴别诊断" placement="right"
                :disabled="!isSidebarCollapsed || env.isMobile.value"
            >
                <rp-icon name="icon-zhenduan" />
            </el-tooltip>
            <span class="text">辅助鉴别诊断</span>
        </div>
        <div class="menu-item" @click="showKnowledgeAssistant">
            <el-tooltip
                effect="dark" content="知识助手" placement="right"
                :disabled="!isSidebarCollapsed || env.isMobile.value"
            >
                <rp-icon name="icon-assistant" />
            </el-tooltip>
            <span class="text">知识助手</span>
        </div>
        <!--
        <div class="menu-item" @click="openNewImagingAnalysisReport">
            <el-tooltip
                effect="dark" content="影像助手" placement="right"
                :disabled="!isSidebarCollapsed || env.isMobile.value"
            >
                <rp-icon name="Picture" size="16" />
            </el-tooltip>
            <span class="text">影像助手</span>
        </div>
        -->
        <div class="menu-item" @click="openNewVoiceMedicalRecord">
            <el-tooltip
                effect="dark" content="语音病例录入" placement="right"
                :disabled="!isSidebarCollapsed || env.isMobile.value"
            >
                <rp-icon name="icon-luyin-outline" />
            </el-tooltip>
            <span class="text">语音病例录入</span>
        </div>
    </div>
</template>

<script setup>
import { useShowDrawer } from '@/ui/index.js';
import ChatBox from '@/views/chat/widgets/ChatBox.vue';
import { ref } from 'vue';
import { useEventBus } from '@vueuse/core';
import { useRouter } from 'vue-router';
import { useEnv } from '@/hooks/useEnv.js';

const env = useEnv();
const knowledgeAssistant = ref(null);
const bus = useEventBus(Symbol('chat'));
bus.on((event, payload) => {
    if (event === 'newChat') {
        knowledgeAssistant.value = payload;
    }
});
const props = defineProps({
    isSidebarCollapsed: {
        type: Boolean,
        default: false
    }
});
const router = useRouter();
const openNewVoiceMedicalRecord = () => {
    router.push({
        name: 'VmaCreate',
        query: {
            t: Date.now()
        }
    });
};
const openNewImagingAnalysisReport = () => {
    router.push({
        name: 'ImagingAnalysisCreate',
        query: {
            t: Date.now()
        }
    });
};
const openNewChat = () => {
    router.push({
        name: 'ChatNew',
        query: {
            t: Date.now()
        }
    });
};
const showKnowledgeAssistant = () => {
    useShowDrawer(ChatBox, {
        id: knowledgeAssistant.value ? knowledgeAssistant.value.id : '',
        scene: 'knowledge',
        title: '我是知识查询助手，需要我帮你查什么呢？',
        subtitle: '关于疾病、药品相关的知识都可以问我哦~',
        eventBus: bus,
        settings: {
            rows: 1,
            tools: {
                // 化验图片
                laboratory: false,
                // 影像图片
                image: false,
            }
        }
    }, {
        title: '知识查询',
        width: '600px',
        bodyClass: 'rp-dialog-body-chatbox',
        direction: 'ltr'
    }).catch(e => {
    }).finally(() => {
        bus.reset();
    });
};
</script>

<style scoped lang="scss">
.appx {
    padding: 8px;
    border-bottom: 1px solid var(--el-border-color);

    .menu-item {
        padding: 8px;
        cursor: pointer;
        font-weight: 500;
        border-radius: 5px;

        i {
            //vertical-align: 0;
        }

        .text {
            margin-left: 5px;
            white-space: nowrap;
        }

        &:hover {
            background: #e5e9f2;
        }
    }

    &.is-collapsed {
        .menu-item {
            .text {
                //display: none;
                writing-mode: vertical-rl; /* 从右向左的竖排 */
                text-orientation: upright; /* 保持字符直立，不旋转 */
                line-height: 16px;
                margin-left: 0;
                display: none;
            }
        }
    }
}

.is-mobile {
    .appx {
        display: none;
    }
}
</style>
