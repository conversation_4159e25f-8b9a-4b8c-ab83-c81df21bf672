<template>
    <div class="iar-report" v-loading="isAnalyzing" element-loading-text="数据分析中…">
        <div class="files">
            <el-upload
                class="image-uploader"
                drag
                multiple
                accept="image/*"
                :http-request="handleUpload"
                :show-file-list="false"
                :auto-upload="false"
                @change="onFileChange"
            >
                <div>
                    <rp-icon name="upload" size="30" />
                    <div class="el-upload__text">影像图片拖到此处，或 <em>点击上传</em></div>
                    <div class="el-upload__tip">支持截图粘贴、拖拽、多文件</div>
                </div>
            </el-upload>
            <div class="image-list">
                <div class="image-item" v-for="(row, idx) in fileList" :key="idx">
                    <files-card
                        :uid="idx"
                        :name="row.name"
                        :url="row.url"
                        :percent="row.percent"
                        :status="row.status"
                        :img-file="row.imgFile"
                        show-del-icon
                        max-width="500px"
                        @delete="handleImageDelete"
                    />
                </div>
            </div>
        </div>
        <div class="actions">
            <el-button type="primary" @click="analyze" v-show="fileList.length > 0">{{ report ? '重新分析' : '开始分析' }}</el-button>
        </div>
        <div class="result" v-if="report">
            <div class="title">AI分析结果</div>
            <div class="content">{{ report.result }}</div>
            <div class="tips">以上内容由AI大模型生成，仅供参考</div>
        </div>
    </div>
</template>

<script setup>
import RpIcon from '@/ui/icon/icon.vue';
import { computed, onMounted, onUnmounted, reactive, ref, watch, watchEffect } from 'vue';
import { uploadCosFile } from '@/hooks/useCos.js';
import { http } from '@/extensions/http.js';
import { httpApis } from '@/hooks/useHttpApi.js';
import { useEventBus } from '@vueuse/core';
import { useToast } from '@/hooks/useToast.js';
const bus = useEventBus('imagingAnalysisBus');

const fileList = ref([]);

const MAX_FILE_COUNT = 4;

const report = ref(null);
const isAnalyzing = ref(false);

const props = defineProps({
    id: {
        type: String,
        default: ''
    }
});

watch(() => props.id, async (newVal) => {
    try {
        if (!newVal) {
            report.value = null;
        }
        else {
            report.value = await getReport(newVal);
        }
    }
    catch (e) {
        console.log(e);
    }
    initFiles();
});
const initFiles = () => {
    if (report.value) {
        fileList.value = report.value.files.map(item => {
            return {
                status: 'done',
                url: item,
                name: item.substr(-10)
            };
        });
    }
    else {
        fileList.value = [];
    }
};
const getReport = async (id) => {
    return await http.get(httpApis.getImagingAnalysisReport, {
        params: {
            id
        }
    }).then(res => res.data);
};
const analyze = async () => {
    const id = report.value ? report.value.id : null;
    isAnalyzing.value = true;
    try {
        const result = await http.post(httpApis.createImagingAnalysisReport, {
            id: id,
            files: fileList.value.map(item => item.url),
            name: ''
        }, {
            local: true
        }).then(res => res.data);
        if (!id) {
            bus.emit('create', result);
        }
        report.value = result;
    }
    catch (e) {
        console.log(e);
    }
    isAnalyzing.value = false;
};
const handleImageDelete = (e) => {
    console.log(e);
    fileList.value.splice(e.uid, 1);
};
// 处理上传
const handleUpload = async ({
    file
}) => {
    if (fileList.value.length >= MAX_FILE_COUNT) {
        useToast( {
            message: '最多只能上传4张图片',
            type: 'warning'
        });
        return;
    }
    const task = reactive({
        status: 'uploading', // 'uploading' | 'done' | 'error'
        url: URL.createObjectURL(file),
        name: file.name,
        imgFile: file,
        percent: 0,
        errorTip: ''
    });
    console.log('task', task);
    fileList.value.push(task);
    try {
        const res = await uploadCosFile(file, {
            onProgress: e => {
                task.percent = e.percent * 100;
            }
        }, 'iar-report');
        task.url = res.url;
        task.status = 'done';
    }
    catch (e) {
        task.status = 'error';
        task.errorTip = e.message;
    }
};

// 粘贴上传
const handlePaste = async (event) => {
    const items = event.clipboardData?.items;
    if (!items) return;

    for (const item of items) {
        if (item.type.startsWith('image/')) {
            const file = item.getAsFile();
            if (file) {
                await handleUpload({
                    file
                });
            }
        }
    }
};

// 拦截 input 文件变化
const onFileChange = (upload) => {
    const files = upload?.target?.files || upload.raw;
    if (!files) return;

    const list = Array.isArray(files) ? files : [files];
    list.forEach(file => {
        handleUpload({
            file
        });
    });
};

onMounted(async () => {
    window.addEventListener('paste', handlePaste);
    if (props.id) {
        report.value = await getReport(props.id);
        initFiles();
    }
});
onUnmounted(() => {
    window.removeEventListener('paste', handlePaste);
});
</script>

<style scoped lang="scss">
.iar-report {
    .actions {
        margin: 10px 0;
    }
    .result {
        //border: 1px solid #ddd;
        //padding: 10px;
        //border-radius: 5px;
        margin-top: 20px;
        .title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .content {
            white-space: pre-wrap;
        }
    }
    .tips {
        font-size: 11px;
        color: #ff5c00;
        margin-top: 5px;
        padding-top: 5px;
    }
    .image-list {
        margin-top: 10px;
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 8px;
        :deep(.elx-files-card) {
            width: 100%;
        }
    }
}
</style>
