/**!
 * Copyright (c) 2017 rppet.cn, Inc. All Rights Reserved
 *
 * Licensed under the Apache License, Version 2.0 (the "License"), you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the License is distributed on
 * an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the License for the
 * specific language governing permissions and limitations under the License.
 *
 * @file Axios 个性化定制
 * <AUTHOR>
 * @since 2022-01-27 15:37
 */
'use strict';
import { reactive } from 'vue';
import axios, { AxiosError } from 'axios';
import { ElMessage as Message, ElLoading as Loading } from 'element-plus';
import { useUserStore } from '@/stores/user';
import { httpApis } from '@/hooks/useHttpApi.js';

const instance = axios.create();
let fullscreenLoading = null;
let cancelSource;

// 当前有效的Ajax请求数
let active = 0;
let loading = false;

// 默认配置项
const defaults = {
    auth: null
};

function setDefaults(opt) {
    Object.assign(defaults, opt);
}

function showLoading() {
    active++;
    setTimeout(() => {
        if (active <= 0) {
            active = 0;
            return;
        }
        fullscreenLoading = Loading.service({
            fullscreen: true,
            background: 'transparent'
        });
        loading = true;
    }, 200);
}
function hideLoading() {
    active--;
    setTimeout(() => {
        if (active <= 0) {
            if (loading) {
                fullscreenLoading.close();
                loading = false;
            }
            active = 0;
        }
    }, 100);
}
function logWithGroup([groupName, style]) {
    if (!window.console) {
        return;
    }
    if (console.groupCollapsed) {
        console.groupCollapsed(groupName, ...style);
        for (let i = 1; i < arguments.length; i++) {
            console.log(...arguments[i]);
        }
        console.groupEnd();
    }
}
instance.defaults.timeout = 30000;
instance.defaults.headers.post['Content-Type'] = 'application/json';
instance.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';

instance.interceptors.request.use(
    config => {
        if (config.rest) {
            config.url = config.url.replace(/:([^/]+)/g, function (a, b) {
                let param = config.rest[b];
                delete config.rest[b];
                return param;
            });
        }
        logWithGroup(
            [
                `%c[发送请求]%c[${config.method.toUpperCase()}] %c${
                    config.url
                }`,
                [
                    'color: blue;',
                    'color: #ab7100;',
                    'color: gray; font-weight: 400;'
                ]
            ],
            [config]
        );
        if (!config.local) {
            // Loading
            showLoading();
        }
        if (cancelSource) {
            config.cancelToken = cancelSource.token;
        }
        // 通过设置config.auth=false来跳过校验
        if (config.auth !== false) {
            const userStore = useUserStore();
            // 检查token有效期，如token过期需要刷新token
            if (!userStore.isLogin) {
                // return session.refreshToken().then(res => {
                //     config.headers.Token = session.getSessionToken();
                //     return config;
                // });
                if (!config.local) {
                    hideLoading();
                }
                return userStore.checkLoginState().then(user => {
                    config.headers['X-Token'] = userStore.getToken();
                    return config;
                });
            }
            config.headers['X-Token'] = userStore.getToken();
        }
        config.adapter = ['fetch', 'xhr'];
        return config;
    },
    err => {
        hideLoading();
        Message.error({
            message: '发送网络请求失败'
        });
        return Promise.reject(err);
    }
);

instance.interceptors.response.use(
    res => {
        logWithGroup(
            [
                `%c[请求响应] %c${res.config.url}`,
                ['color: green;', 'color: gray; font-weight: 400;']
            ],
            ['数据', res.data],
            ['XMLHttpRequest', res.request],
            ['Headers', res.headers],
            ['配置信息', res.config]
        );

        hideLoading();
        if (res.config.responseType === 'stream' || res.config.responseType === 'arraybuffer') {
            return Promise.resolve(res);
        }
        const json = res.data;
        return new Promise((resolve, reject) => {
            if (json.success || json.code === 0) {
                resolve({
                    config: res.config,
                    headers: res.headers,
                    status: res.status,
                    request: res.request,
                    originData: res.data,
                    data: json.data || json.result
                });
            }
            else {
                if (!res.config.noErrorTip) {
                    Message.error({
                        message: json.message || json.msg || '操作出错了'
                    });
                }
                const error = new Error(json.message || json.msg);
                error.config = res.config;
                error.request = res.request;
                error.response = res;
                reject(error);
            }
        });
    },
    err => {
        hideLoading();
        if (!axios.isCancel(err)) {
            const res = err.response;
            if (err instanceof AxiosError) {
                if (res && res.status === 401) {
                    const userStore = useUserStore();
                    // 清理用户信息
                    userStore.clearUser();
                    if (typeof defaults.auth === 'function') {
                        defaults.auth();
                        return Promise.reject(new Error('用户未登录'));
                    }
                    else {
                        if (err.config.retry || err.config.url === httpApis.getLoginInfo) {
                            return Promise.reject(new Error('用户未登录'));
                        }
                        return userStore.checkLoginState(true).then(res => {
                            // 登录成功重新请求
                            err.config.retry = true;
                            return instance(err.config);
                        });
                    }
                }
                else if (res) {
                    logWithGroup(
                        [
                            `%c[请求失败] %c${err.config.url}`,
                            ['color: red;', 'color: gray; font-weight: 400;']
                        ],
                        ['数据', res.data],
                        ['Headers', res.headers],
                        ['XMLHttpRequest', res.request],
                        ['配置信息', res.config]
                    );
                    if (!err.config.noErrorTip) {
                        Message.error({
                            message: (res && res.data.msg) || err.message || '获取数据失败，请稍后重试！'
                        });
                    }
                }
                else {
                    console.log('e', err);
                    if (!err.config.noErrorTip) {
                        Message.error({
                            message: (res && res.data.msg) || err.message || '获取数据失败，请稍后重试！'
                        });
                    }
                }
            }
            else if (err instanceof TypeError) {
                Message.error({
                    message: err.message
                });
                return Promise.reject(err);
            }
        }
        return Promise.reject(err);
    }
);

function install(app, router, options) {
    if (options) {
        setDefaults(options);
    }
    if (router) {
        let lastPage = null;
        let skip = false;
        router.beforeEach(function (to, from) {
            if (lastPage && lastPage.fullPath === to.fullPath) {
                skip = true;
                return;
            }
            lastPage = to;

            if (cancelSource && to.fullPath !== from.fullPath) {
                cancelSource.cancel('页面离开，取消请求');
            }
            cancelSource = axios.CancelToken.source();
            showLoading();
        });
        router.afterEach(route => {
            if (skip) {
                skip = false;
                return;
            }
            hideLoading();
        });
    }

    app.config.globalProperties.$http = instance;
}

instance.CancelToken = axios.CancelToken;
instance.isCancel = axios.isCancel;
export const http = instance;

export function usePostRequest(url, data, axiosOption, options) {
    const result = reactive({
        loading: false,
        data: null,
        error: null,
        message: null,
        execute: () => {
            if (result.loading) {
                return;
            }
            result.loading = true;
            result.data = null;
            result.error = null;
            return http.post(url, data, axiosOption).then(res => {
                result.data = res.data;
                options?.onSuccess(res);
            })
                .catch(e => {
                    result.error = e;
                    options?.onError(e);
                })
                .finally(() => {
                    result.loading = false;
                });
        },
        cancel: () => {
            if (result.loading) {
                cancelSource.cancel('请求取消');
            }
        }
    });
    if (options.immediate) {
        result.execute();
    }
    return result;
}

export default install;
