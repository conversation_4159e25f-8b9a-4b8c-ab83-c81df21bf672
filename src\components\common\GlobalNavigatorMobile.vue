<template>
    <el-dropdown @command="handleCommand">
        <span class="el-dropdown-link">
            <slot></slot>
            <rp-icon class="icon" name="arrow-down" />
        </span>
        <template #dropdown>
            <el-dropdown-menu>
                <el-dropdown-item command="ChatNew">
                    <rp-icon class="icon" name="icon-new-chat" />
                    辅助鉴别诊断
                </el-dropdown-item>
                <el-dropdown-item command="Knowledge">
                    <rp-icon class="icon" name="icon-assistant" />
                    知识助手
                </el-dropdown-item>
                <!--
                <el-dropdown-item command="ImagingAnalysisCreate">
                    <rp-icon class="icon" name="Picture" />
                    影像助手
                </el-dropdown-item>
                -->
                <el-dropdown-item command="VmaCreate">
                    <rp-icon class="icon" name="icon-luyin-outline" />
                    语音病例助手
                </el-dropdown-item>
            </el-dropdown-menu>
        </template>
    </el-dropdown>
</template>

<script setup>
import { useRouter } from 'vue-router';
import RpIcon from '@/ui/icon/icon.vue';
const router = useRouter();
const CommandMap = {
    ChatNew: {
        name: 'ChatNew',
        query: {}
    },
    Knowledge: {
        name: 'ChatNew',
        query: {
            scene: 'knowledge'
        }
    },
    VmaCreate: {
        name: 'VmaCreate',
        query: {}
    },
    ImagingAnalysisCreate: {
        name: 'ImagingAnalysisCreate',
        query: {}
    }
};
const handleCommand = (command) => {
    const cmd = CommandMap[command];
    console.log(cmd);
    router.push({
        name: cmd.name,
        query: {
            ...cmd.query,
            t: Date.now()
        }
    });
};
</script>

<style scoped lang="scss">
.el-dropdown-link {
    outline: none;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    .icon {
        vertical-align: -2px;
        margin-left: 4px;
    }
}
</style>
