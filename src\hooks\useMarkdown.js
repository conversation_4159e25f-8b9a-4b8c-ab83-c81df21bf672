// useMarkdown.ts
import MarkdownIt from 'markdown-it';
import hljs from 'highlight.js';
// import katex from 'katex';
import 'katex/dist/katex.min.css';
import markdownItKatex from 'markdown-it-katex';

// console.log(MarkdownMermaid, markdownItKatex);
import { markdownItSafeHtml } from '@/components/markdown/markdownItSafeHtml';
import { nextTick } from 'vue';

export function useMarkdownRenderer() {
    const md = MarkdownIt({
        html: true,
        linkify: false,
        breaks: false,
        highlight(code, lang) {
            if (lang === 'mermaid') {
                const id = `mermaid-${Math.random().toString(36).substr(2, 9)}`;
                return `<div class="mermaid" id="${id}">${code}</div>`;
            }
            if (lang && hljs.getLanguage(lang)) {
                return `<pre><code class="hljs language-${lang}">${hljs.highlight(code, {
                    language: lang
                }).value}</code></pre>`;
            }
            return `<pre><code class="hljs">${md.utils.escapeHtml(code)}</code></pre>`;
        }
    });

    md.use(markdownItKatex);
    // md.use(markdownItSafeHtml);

    return md;
}
export const renderMermaidDiagrams = async (container) => {
    // 动态导入 mermaid，避免 SSR 问题
    if (typeof window === 'undefined') return;

    try {
        const mermaid = await import('mermaid');

        // 初始化 mermaid
        mermaid.default.initialize({
            startOnLoad: false,
            theme: 'default',
            securityLevel: 'loose',
            fontFamily: 'inherit'
        });

        await nextTick();

        const mermaidElements = container.querySelectorAll('.mermaid');

        for (let i = 0; i < mermaidElements.length; i++) {
            const element = mermaidElements[i];
            const graphDefinition = element.textContent.trim();

            try {
                const pEl = element.parentNode;
                const {
                    svg
                } = await mermaid.default.render(element.id, graphDefinition);
                pEl.innerHTML = svg;
                // element.innerHTML = svg;
                // element.classList.add('mermaid-rendered');
            }
            catch (error) {
                console.error('Mermaid rendering error:', error);
                element.innerHTML = `<div class="mermaid-error">
            <p>Failed to render diagram</p>
            <details>
              <summary>Error details</summary>
              <pre>${error.message}</pre>
            </details>
          </div>`;
            }
        }
    }
    catch (error) {
        console.error('Failed to load mermaid:', error);
    }
};
