<template>
    <div class="login-container">
        <div style="margin-bottom: 10px;">
            根据国家相关法律规定及网络实名制要求，请验证有效手机号码
        </div>
        <el-form :model="formData" :rules="rules" ref="formRef" class="login-form">
            <el-form-item prop="phone">
                <el-input
                    v-model="formData.phone"
                    placeholder="请输入手机号码"
                    :maxlength="11"
                >
                    <template #prefix>
                        <rp-icon name="iphone" />
                    </template>
                </el-input>
            </el-form-item>

            <el-form-item prop="code">
                <el-input
                    v-model="formData.code"
                    placeholder="验证码"
                    :maxlength="6"
                >
                    <template #prefix>
                        <rp-icon name="lock" />
                    </template>
                    <template #suffix>
                        <a
                            href="javascript:void(0)"
                            :class="{ 'is-disabled': isCountingDown }"
                            @click="!isCountingDown && handleSendCode()"
                        >
                            {{ countDownText }}
                        </a>
                    </template>
                </el-input>
            </el-form-item>

            <el-form-item prop="agreement">
                <el-checkbox v-model="formData.agreement" class="agreement-checkbox">
                    已阅读并同意
                    <a href="javascript:void(0)" @click="showServiceAgreement">《服务协议》</a>
                    与
                    <a href="javascript:void(0)" @click="showPrivacyPolicy">《隐私政策》</a>
                    ，未注册的手机号将自动注册
                </el-checkbox>
            </el-form-item>

            <el-button type="primary" class="login-btn" @click="handleLogin" :loading="loading">
                登录
            </el-button>
        </el-form>
    </div>
</template>

<script setup>
import { ref, reactive, defineEmits } from 'vue';
import { ElMessage } from 'element-plus';
import { useUserStore } from '@/stores/user';
import { httpApis } from '@/hooks/useHttpApi';
import { http } from '@/extensions/http';
import Privacy from '@/views/misc/privacy.vue';
import Agreement from '@/views/misc/agreement.vue';
import PageView from '@/views/misc/page.vue';
import { useShowDialog } from '@/ui/index';
const userStore = useUserStore();

const emit = defineEmits(['ok']);
const formRef = ref(null);
const loading = ref(false);
const countdown = ref(60);
const isCountingDown = ref(false);
const countDownText = ref('发送验证码');

const formData = reactive({
    phone: '',
    code: '',
    agreement: false
});

const rules = {
    phone: [
        {
            required: true, message: '请输入手机号码', trigger: 'blur'
        },
        {
            pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur'
        }
    ],
    code: [
        {
            required: true, message: '请输入验证码', trigger: 'blur'
        },
        {
            pattern: /^\d{6}$/, message: '验证码格式不正确', trigger: 'blur'
        }
    ],
    agreement: [
        {
            validator: (rule, value, callback) => {
                if (!value) {
                    callback(new Error('请阅读并同意服务协议和隐私政策'));
                }
                else {
                    callback();
                }
            },
            trigger: 'change'
        }
    ]
};

const startCountDown = () => {
    isCountingDown.value = true;
    countdown.value = 60;
    const timer = setInterval(() => {
        countdown.value--;
        countDownText.value = `${countdown.value}秒后重试`;
        if (countdown.value <= 0) {
            clearInterval(timer);
            isCountingDown.value = false;
            countDownText.value = '发送验证码';
        }
    }, 1000);
};

const handleSendCode = async () => {
    try {
        await formRef.value?.validateField('phone');
        // 调用发送验证码接口
        http.post(httpApis.sendSmsCode, {
            mobile: formData.phone
        }, {
            auth: false
        }).then((res) => {
            ElMessage.success('验证码发送成功');
            startCountDown();
        });
    }
    catch (error) {
        console.error(error);
    }
};

const handleLogin = async () => {
    if (!formRef.value) return;

    try {
        await formRef.value.validate();
        loading.value = true;
        // 调用登录接口
        http.post(httpApis.loginByMobile, {
            mobile: formData.phone,
            code: formData.code
        }, {
            auth: false
        }).then((res) => {
            userStore.login(res.data);
            emit('ok', res.data);
        });
    }
    catch (error) {
        console.error(error);
    }
    finally {
        loading.value = false;
    }
};

// 显示服务协议
const showServiceAgreement = () => {
    useShowDialog(PageView, {
        height: '500px',
        component: Agreement
    }, {
        width: '800px',
        title: '服务协议'
    });
};

// 显示隐私政策
const showPrivacyPolicy = () => {
    useShowDialog(PageView, {
        height: '500px',
        component: Privacy
    }, {
        width: '800px',
        title: '隐私政策'
    });
};
</script>

<style scoped lang="scss">
.login-container {
    width: 100%;
    max-width: 480px;
    margin: 0 auto;
    //padding: 20px;
}

.login-form {
    :deep(.el-input__wrapper) {
        height: 40px;
    }

    :deep(.el-input__suffix) {
        .el-link {
            margin-right: 8px;
            font-size: 14px;

            &.is-disabled {
                color: #999;
                cursor: not-allowed;
            }
        }
    }

    .agreement-checkbox {
        word-wrap: break-word;
        white-space: wrap;
    }
    .login-btn {
        width: 100%;
        //margin-top: 20px;
        height: 40px;
        font-size: 16px;
    }

    :deep(.el-checkbox__label) {
        color: #666;
        font-size: 12px;
    }
}
</style>
