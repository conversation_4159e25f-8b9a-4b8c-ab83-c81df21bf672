<script lang="ts" setup>
import type { BookInfo } from "@/services/knowledgeService";
import { useChatStore } from "@/stores/chat";
import { storeToRefs } from "pinia";
import { computed, onMounted, onUnmounted, ref } from "vue";

// 全局的Pina库
const chatStore = useChatStore();

// 全局的响应数据
const { messageList } = storeToRefs(chatStore);

//本组件的响应数据
const bubbleList = computed(() => {
  return messageList.value.map((item) => {
    return {
      ...item,
      key: item.id,
      role: item.type,
      placement: item.type === "ai" ? "start" : "end",
      content: item.content,
      loading: item.thinkLoading,
      finished: !item.loading,
      isMarkdown: item.type === "ai",
      avatar: item.type === "ai" ? "logo.png" : "",
      avatarSize: "48px",
    };
  });
});

// Popover 相关状态和方法
const popoverVisible = ref(false);
const popoverContent = ref('');
const popoverTitle = ref('');
const popoverPages = ref('');
const popoverPosition = ref({ x: 0, y: 0 });
let hideTimer: NodeJS.Timeout | null = null;
const getNoRepateBooks = (books: BookInfo[]) => {
  const noRepateBooks: BookInfo[] = [];
  if (books && books.length > 0) {
    const seenNames = new Set<string>();
    for (let i = 0; i < books.length; i++) {
      const book = books[i];
      if (!seenNames.has(book.name)) {
        seenNames.add(book.name);
        noRepateBooks.push(book);
      }
    }
  }
  return noRepateBooks;
};

// 显示Popover
const showPopover = (target: HTMLElement) => {
  if (hideTimer) {
    clearTimeout(hideTimer);
    hideTimer = null;
  }

  const content = target.getAttribute('data-content');
  const name = target.getAttribute('data-name');
  const pages = target.getAttribute('data-pages') ?? "";

  if (content && name) {
    popoverContent.value = content;
    popoverTitle.value = name;
    popoverPages.value = pages;

    // 计算popover位置
    const rect = target.getBoundingClientRect();
    popoverPosition.value = {
      x: rect.left + rect.width / 2,
      y: rect.top - 10
    };

    popoverVisible.value = true;
  }
};

// 隐藏Popover
const hidePopover = () => {
  if (hideTimer) {
    clearTimeout(hideTimer);
  }

  hideTimer = setTimeout(() => {
    popoverVisible.value = false;
    hideTimer = null;
  }, 150);
};

// 取消隐藏（当鼠标移到Popover上时）
const cancelHide = () => {
  if (hideTimer) {
    clearTimeout(hideTimer);
    hideTimer = null;
  }
};

// 滚动事件处理（只在页面滚动时隐藏Popover）
const handleScroll = (event: Event) => {
  // 只有当滚动的不是Popover内部时才隐藏
  const target = event.target as HTMLElement;
  const popoverElement = document.querySelector('.document-popover');

  // 如果是Popover内部的滚动，不隐藏
  if (popoverElement && (target === popoverElement || popoverElement.contains(target))) {
    return;
  }

  // 页面或其他容器滚动时隐藏Popover
  if (popoverVisible.value) {
    popoverVisible.value = false;
    if (hideTimer) {
      clearTimeout(hideTimer);
      hideTimer = null;
    }
  }
};

// 全局事件监听器
const handleGlobalMouseOver = (event: Event) => {
  const target = event.target as HTMLElement;
  if (target && target.classList.contains('document-book') && target.classList.contains('circled-number')) {
    showPopover(target);
  }
};

const handleGlobalMouseOut = (event: Event) => {
  const target = event.target as HTMLElement;
  if (target && target.classList.contains('document-book') && target.classList.contains('circled-number')) {
    hidePopover();
  }
};

// 组件挂载和卸载时的处理
onMounted(() => {
  document.addEventListener('mouseover', handleGlobalMouseOver);
  document.addEventListener('mouseout', handleGlobalMouseOut);
  // 添加滚动监听器，使用capture模式
  window.addEventListener('scroll', handleScroll, true);
});

onUnmounted(() => {
  document.removeEventListener('mouseover', handleGlobalMouseOver);
  document.removeEventListener('mouseout', handleGlobalMouseOut);
  // 移除滚动监听器
  window.removeEventListener('scroll', handleScroll, true);
  // 清理定时器
  if (hideTimer) {
    clearTimeout(hideTimer);
    hideTimer = null;
  }
});
</script>

<template>
  <div class="bubble-list-container">
    <BubbleList :list="bubbleList" max-height="90dvh" class="bubble-list"
      :backButtonPosition="{ bottom: '20dvh', left: 'calc(50% - 19px)' }">
      <template #footer="{ item }">
        <div class="footer-container">
          <div v-if="item.books && item.books.length > 0" class="footer-book">
            <div>参考文献：</div>
            <div class="footer-book-list">
              <div class="footer-book-item" v-for="book in getNoRepateBooks(item.books)" :key="book.id">
                <div>{{ book.name }}</div>
              </div>
            </div>
          </div>
          <div class="footer-remarks" v-if="item.role === 'ai' && item.finished">
            本文由 AI 生成，仅供医学参考，不能替代专业诊疗意见。请以临床医师的最终判断为准
          </div>
        </div>
      </template>
    </BubbleList>

    <!-- Popover 弹出框 -->
    <Teleport to="body">
      <div v-if="popoverVisible" class="document-popover" :style="{
        left: popoverPosition.x + 'px',
        top: popoverPosition.y + 'px'
      }" @mouseenter="cancelHide" @mouseleave="hidePopover">
        <div class="popover-header">
          <strong>{{ popoverTitle }}</strong>
          <span v-if="!!popoverPages" class="popover-header-pages">（{{ popoverPages }}）</span>
        </div>
        <div class="popover-content">
          {{ popoverContent }}
        </div>
      </div>
    </Teleport>
  </div>
</template>
<style scoped src="./BubbleList.css"></style>
